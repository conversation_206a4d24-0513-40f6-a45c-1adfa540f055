import streamlit as st
import os
import sys
import xlsxwriter
import pandas as pd
import numpy as np
from datetime import datetime
from sipModules import validate_sip_dates, XIRRCalculator, FinancialMetrics, NonSipPsuedoNav, SIPAllocator

# ✅ This must come first
st.set_page_config(page_title="SUPER SIP Simulator", layout="wide")


# --- Load NAV data ---
@st.cache_data
def load_data():
    df = pd.read_csv("input_data/nav_data.csv", parse_dates=["Date"], index_col=0)
    df = df.dropna()
    return df
df = load_data()
funds = df.columns
benchmarks = ["Nifty 50", "Nifty 500", "GOLD(Ounce/INR)"]

st.title("🧮 SUPER SIP Simulator")

# Sidebar — SIP Settings
st.sidebar.header("2️⃣ SIP Settings")
monthly_investment = st.sidebar.number_input("Monthly SIP Amount (₹)", value=100000, step=1000)
start_date = st.sidebar.date_input("Start Date", df.index.min().date())
end_date = st.sidebar.date_input("End Date", df.index.max().date())
benchmark_choice = st.sidebar.selectbox("Select Benchmark", benchmarks)
annual_tracking_error = st.sidebar.slider("Tracking Error (%)", min_value=0.0, max_value=5.0, value=1.5, step=0.25)
management_fee = st.sidebar.slider("Management Fees (%)", min_value=0.0, max_value=5.0, value=2.5, step=0.25)
expense_ratio = st.sidebar.slider("Expense Ratio (%)", min_value=0.0, max_value=5.0, value=1.0, step=0.25)

# Main Panel — Portfolio Weight Editor
st.header("1️⃣ Custom Portfolio Allocation")

weights_df = pd.DataFrame({"Fund": funds, "Weight (%)": [0 for _ in funds]})
edited_df = st.data_editor(weights_df, num_rows="fixed", use_container_width=True)
total_weight = edited_df["Weight (%)"].sum()

# Check total weight
if total_weight != 100:
    st.error(f"⚠️ Total allocation must be exactly 100%. Current: {total_weight:.1f}%")
    st.stop()

weights = dict(zip(edited_df["Fund"], edited_df["Weight (%)"] / 100))
df_filtered = df.loc[start_date:end_date]
if df_filtered.empty:
    st.warning("No data in the selected date range.")
    st.stop()

# SIP Simulation
st.header("📈 Portfolio NAV & Performance")
portfolio_nav = pd.Series(0.0, index=df_filtered.index)
# Generate SIP Dates and Validate 
sip_dates = pd.date_range(start=df_filtered.index.min(), end=df_filtered.index.max(), freq='MS') + pd.Timedelta(days=1)
# from sipModules
validated_sip_dates = validate_sip_dates(sip_dates, df_filtered)
num_years = len(validated_sip_dates)/12
# from sipModules
allocator = SIPAllocator(
    component_navs=df_filtered[funds],
    sip_dates=validated_sip_dates,
    weights=weights,
    monthly_investment=monthly_investment
    )
component_navs, portfolio_weights, units_df = allocator.allocate_units()
# Final Portfolio Value Calculation
sip_portfolio_value = (units_df * df_filtered[funds]).sum(axis=1)
df_filtered["Portfolio NAV"] = sip_portfolio_value

# --- Benchmark NAV Simulation (using SIP into benchmark) ---
benchmark_nav = df_filtered[benchmark_choice]
benchmark_units = []
for date in validated_sip_dates:
    # if date in benchmark_nav.index:
    units = monthly_investment / benchmark_nav.loc[date]
    benchmark_units.append((date, units))

benchmark_units_df = pd.DataFrame(benchmark_units, columns=["Date", "Units"]).set_index("Date")
cumulative_units = benchmark_units_df["Units"].cumsum()
benchmark_sip_nav = cumulative_units * benchmark_nav.loc[validated_sip_dates]
benchmark_sip_nav = benchmark_sip_nav.reindex(df_filtered.index).ffill()
df_filtered["Benchmark NAV"] = benchmark_sip_nav


# --- XIRR Calculations ---
portfolio_cash_flows = [{'date': d, 'value': -monthly_investment} for d in validated_sip_dates]
portfolio_cash_flows.append({'date': sip_portfolio_value.index[-1], 'value': sip_portfolio_value.iloc[-1]})
xirr_portfolio = XIRRCalculator(portfolio_cash_flows).compute()

benchmark_cash_flows = [{'date': d, 'value': -monthly_investment} for d in validated_sip_dates]
benchmark_cash_flows.append({'date': benchmark_sip_nav.index[-1], 'value': benchmark_sip_nav.iloc[-1]})
xirr_benchmark = XIRRCalculator(benchmark_cash_flows).compute()

# Adjusted NAV with tracking error, fees
non_sip = NonSipPsuedoNav(component_navs, portfolio_weights, initial_corpus=100)
non_sip_df = non_sip.build_portfolio(annual_tracking_error/100, management_fee/100, expense_ratio/100)
adjusted_nav_series = non_sip.get_adjusted_nav_series()

# Generate Financial Metrics
fm_portfolio = FinancialMetrics(nav_df=adjusted_nav_series, sip_amount=monthly_investment, sip_dates=validated_sip_dates)
metrics_portfolio = fm_portfolio.generate_metrics()

fm_benchmark = FinancialMetrics(nav_df=benchmark_nav, sip_amount=monthly_investment, sip_dates=validated_sip_dates)
metrics_benchmark = fm_benchmark.generate_metrics()

# --- UI Tabs ---
tab1, tab2, tab3 = st.tabs(["📊 Portfolio NAV", "📈 Performance Metrics", "📁 Save Portfolio"])

with tab1:
    st.line_chart(df_filtered[["Portfolio NAV", "Benchmark NAV"]])

with tab2:
    st.subheader("📈 SIP Performance Comparison")

    def format_metric_row(label, pval, bval, format_type='%', digits=2):
        if format_type == "%":
            fmt = f"{{:.{digits}%}}"
        elif format_type == "₹":
            fmt = f"₹{{:,.0f}}"
        elif format_type == "f":
            fmt = f"{{:.{digits}f}}"
        else:
            fmt = "{}"  # fallback plain format

        try:
            pval_fmt = fmt.format(pval) if isinstance(pval, (int, float)) else pval
        except:
            pval_fmt = "N/A"

        try:
            bval_fmt = fmt.format(bval) if isinstance(bval, (int, float)) else bval
        except:
            bval_fmt = "N/A"

        return [label, pval_fmt, bval_fmt]

    comparison_data = []
    comparison_data.append(format_metric_row("Total Invested", metrics_portfolio.get("Total Invested", None), metrics_benchmark.get("Total Invested", None), format_type="₹"))
    comparison_data.append(format_metric_row("Final Value", sip_portfolio_value.iloc[-1], benchmark_sip_nav.iloc[-1], format_type="₹"))
    comparison_data.append(format_metric_row("Investment Horizon (Yr)", num_years, num_years, format_type='f'))
    comparison_data.append(format_metric_row("XIRR", xirr_portfolio, xirr_benchmark, format_type="%"))
    comparison_data.append(format_metric_row("CAGR", metrics_portfolio.get("CAGR", None), metrics_benchmark.get("CAGR", None), format_type="%"))
    comparison_data.append(format_metric_row("MDD", metrics_portfolio.get("MDD", None), metrics_benchmark.get("MDD", None), format_type="%"))
    comparison_data.append(format_metric_row("Sharpe", metrics_portfolio.get("Sharpe", None), metrics_benchmark.get("Sharpe", None), format_type="f"))
    comparison_data.append(format_metric_row("Sortino", metrics_portfolio.get("Sortino", None), metrics_benchmark.get("Sortino", None), format_type="f"))
    comparison_data.append(format_metric_row("1Y Volatility", metrics_portfolio.get("Rolling_1Y_Volatility", None), metrics_benchmark.get("Rolling_1Y_Volatility", None), format_type="%"))
    comparison_data.append(format_metric_row("1Y Return Mean", metrics_portfolio.get("Rolling_1Y_Mean_Return", None), metrics_benchmark.get("Rolling_1Y_Mean_Return", None), format_type="%"))
    comparison_data.append(format_metric_row("3Y Return Mean", metrics_portfolio.get("Rolling_3Y_Mean_Return", None), metrics_benchmark.get("Rolling_3Y_Mean_Return", None), format_type="%"))
    comparison_data.append(format_metric_row("5Y Return Mean", metrics_portfolio.get("Rolling_5Y_Mean_Return", None), metrics_benchmark.get("Rolling_7Y_Mean_Return", None), format_type="%"))
    comparison_data.append(format_metric_row("7Y Return Mean", metrics_portfolio.get("Rolling_7Y_Mean_Return", None), metrics_benchmark.get("Rolling_5Y_Mean_Return", None), format_type="%"))
    comparison_data.append(format_metric_row("10Y Return Mean", metrics_portfolio.get("Rolling_10Y_Mean_Return", None), metrics_benchmark.get("Rolling_10Y_Mean_Return", None), format_type="%"))
    comparison_data.append(format_metric_row("1Y Return Median", metrics_portfolio.get("Rolling_1Y_Median_Return", None), metrics_benchmark.get("Rolling_1Y_Mean_Return", None), format_type="%"))
    comparison_data.append(format_metric_row("3Y Return Median", metrics_portfolio.get("Rolling_3Y_Median_Return", None), metrics_benchmark.get("Rolling_3Y_Mean_Return", None), format_type="%"))
    comparison_data.append(format_metric_row("5Y Return Median", metrics_portfolio.get("Rolling_5Y_Median_Return", None), metrics_benchmark.get("Rolling_7Y_Mean_Return", None), format_type="%"))
    comparison_data.append(format_metric_row("7Y Return Median", metrics_portfolio.get("Rolling_7Y_Median_Return", None), metrics_benchmark.get("Rolling_5Y_Mean_Return", None), format_type="%"))
    comparison_data.append(format_metric_row("10Y Return Median", metrics_portfolio.get("Rolling_10Y_Median_Return", None), metrics_benchmark.get("Rolling_10Y_Median_Return", None), format_type="%"))
    
    comparison_data.append(format_metric_row("Jensen Alpha", metrics_portfolio.get("Jensen Alpha", None), metrics_benchmark.get("Jensen Alpha", None), format_type="%"))
    comparison_data.append(format_metric_row("Upside Capture Ratio", metrics_portfolio.get("Upside Capture Ratio", None), metrics_benchmark.get("Upside Capture Ratio", None), format_type="%"))
    comparison_data.append(format_metric_row("Downside Capture Ratio", metrics_portfolio.get("Upside Capture Ratio", None), metrics_benchmark.get("Downside Capture Ratio", None), format_type="%"))

    comp_df = pd.DataFrame(comparison_data, columns=["Metric", "Portfolio", benchmark_choice])
    st.dataframe(comp_df, use_container_width=True)

with tab3:
    st.subheader("Save Portfolio")
    portfolio_name = st.text_input("Enter a name for this portfolio", placeholder="e.g., MultiAsset50 or SIP_Balanced")
    save_button = st.button("💾 Save Portfolio Stats")
    os.makedirs("saved_portfolios", exist_ok=True)

    if save_button:
        if not portfolio_name:
            st.warning("Please enter a portfolio name before saving.")
        else:
            non_zero_weights = {f: w for f, w in weights.items() if w > 0}
            weight_str = ", ".join([f"{f} ({w*100:.1f}%)" for f, w in non_zero_weights.items()])
            output_row = {
                "Portfolio Name": portfolio_name,
                "Fund Allocations": weight_str,
                "Start Date": start_date,
                "End Date": end_date,
                "Monthly SIP (₹)": monthly_investment,
                "Final Value": sip_portfolio_value.iloc[-1],
                "XIRR": xirr_portfolio,
                "CAGR": metrics_portfolio["CAGR"],
                "Sharpe": metrics_portfolio["Sharpe"],
                "Sortino": metrics_portfolio["Sortino"],
                "MDD": metrics_portfolio["MDD"],
                "Benchmark": benchmark_choice,
                "Benchmark XIRR": xirr_benchmark,
                "Benchmark CAGR": metrics_benchmark["CAGR"],
                "Benchmark MDD": metrics_benchmark["MDD"],
                "Tracking Error": annual_tracking_error,
                "Fees (%)": management_fee,
                "Expense Ratio (%)": expense_ratio,
            }
            pd.DataFrame([output_row]).to_excel(f"saved_portfolios/{portfolio_name}.xlsx", index=False)
            st.success(f"✅ Portfolio '{portfolio_name}' saved.")

