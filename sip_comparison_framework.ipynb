import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, date
import warnings
warnings.filterwarnings("ignore")

print("📦 Libraries imported successfully!")

aum_sheet = pd.read_excel('MF_NAV/Scheme Names - Traditional SIP.xlsx', sheet_name='AUM')
aum_sheet = aum_sheet.iloc[33:, 1:]
aum_sheet.columns = aum_sheet.iloc[1]
aum_sheet = aum_sheet.iloc[2:, :]
aum_sheet = aum_sheet.dropna(how='all', axis=1)
aum_sheet = aum_sheet.dropna(how='all', axis=0)
aum_sheet.index = aum_sheet.iloc[:, 0]
aum_sheet = aum_sheet.iloc[:, 1:]
aum_sheet = aum_sheet.apply(pd.to_numeric, errors='coerce')
aum_sheet.index.name = 'Date'
aum_sheet.index = pd.to_datetime(aum_sheet.index) 
aum_sheet.head()
vintage_sheet = pd.read_excel('MF_NAV/Scheme Names - Traditional SIP.xlsx', sheet_name='Vintage')
vintage_sheet = vintage_sheet.iloc[32:, 1:]
vintage_sheet.columns = vintage_sheet.iloc[1]
vintage_sheet = vintage_sheet.iloc[2:, :]
vintage_sheet = vintage_sheet.dropna(how='all', axis=1)
vintage_sheet = vintage_sheet.dropna(how='all', axis=0)
vintage_sheet.index = vintage_sheet.iloc[:, 0]
vintage_sheet = vintage_sheet.iloc[:, 1:]
vintage_sheet = vintage_sheet.apply(pd.to_numeric, errors='coerce')
vintage_sheet.index.name = 'Date'
vintage_sheet.index = pd.to_datetime(vintage_sheet.index)
vintage_sheet.head()

# Merge the datasets
# remove duplicate funds
df = aum_sheet.merge(vintage_sheet, left_index=True, right_index=True, how='outer', suffixes=('', '_drop')).ffill()
# drop the duplicate columns
df = df.loc[:, ~df.columns.str.endswith('_drop')]

print(f"📊 Loaded NAV data: {df.shape}")
print(f"📅 Date range: {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}")
print(f"💼 Available funds: {len(df.columns)}")

class SIPAnalyzer:
    def __init__(self, nav_data):
        """
        Simple SIP Analyzer for equal-weighted portfolios
        """
        self.nav_data = nav_data.copy()
        self.available_funds = list(nav_data.columns)
        print(f"🎯 SIP Analyzer initialized with {len(self.available_funds)} funds")
        print("✅ Framework configured for: Equal weights, No rebalancing, XIRR focus")
        print("✅ Auto start date: Uses shortest fund's inception date for fair comparison")
        print("✅ Complete results: Displays ALL portfolios, not just top performers")
        
    def verify_principles(self):
        """Verify that framework follows the core principles"""
        print("\n🔍 FRAMEWORK VERIFICATION:")
        print("✅ Equal weights: Always 20% per fund (monthly_amount / 5)")
        print("✅ No rebalancing: Units accumulate, no selling or rebalancing")
        print("✅ Fixed portfolio: Same 5 funds throughout investment period")
        print("✅ XIRR calculation: Cash flow based returns only")
        print("✅ SIP simulation: Fixed monthly investment, natural growth")
        return True
        
    def show_funds(self):
        """Display all available funds"""
        print("\n📋 Available Funds:")
        print("-" * 80)
        for i, fund in enumerate(self.available_funds):
            print(f"{i:2d}: {fund}")
        print("-" * 80)
        
    def simulate_sip(self, fund_indices, monthly_amount=100000, start_date=None, end_date=None):
        """
        Simulate SIP for 5 equal-weighted funds
        
        Parameters:
        fund_indices (list): List of 5 fund indices
        monthly_amount (float): Monthly SIP amount
        start_date (str): Start date (YYYY-MM-DD) - if None, uses shortest fund's inception date
        end_date (str): End date (YYYY-MM-DD)
        
        Returns:
        dict: SIP simulation results
        """
        if len(fund_indices) != 5:
            raise ValueError("Please select exactly 5 funds")
            
        # Select funds
        selected_funds = [self.available_funds[i] for i in fund_indices]
        fund_data = self.nav_data[selected_funds]
        
        # Find the shortest fund's inception date (latest start date among selected funds)
        # This ensures all funds have data from the start date
        fund_inception_dates = []
        for fund in selected_funds:
            first_valid_date = fund_data[fund].first_valid_index()
            if first_valid_date is not None:
                fund_inception_dates.append(first_valid_date)
        
        if not fund_inception_dates:
            raise ValueError("No valid data found for selected funds")
        
        # Use the latest inception date (shortest fund's history)
        shortest_fund_inception = max(fund_inception_dates)
        
        # Set date range
        if start_date:
            start_date = pd.to_datetime(start_date)
            # Ensure start_date is not before shortest fund's inception
            start_date = max(start_date, shortest_fund_inception)
        else:
            start_date = shortest_fund_inception
            
        if end_date:
            end_date = pd.to_datetime(end_date)
        else:
            end_date = fund_data.index[-1]
            
        # Filter data and drop rows with any NaN values after setting proper start date
        sip_data = fund_data.loc[start_date:end_date].dropna()
        
        # Generate SIP dates (1st of each month)
        sip_dates = []
        current = start_date.replace(day=1)
        
        while current <= end_date:
            # Find nearest trading day
            available = sip_data.index[sip_data.index >= current]
            if len(available) > 0 and available[0] <= end_date:
                sip_dates.append(available[0])
            
            # Next month
            if current.month == 12:
                current = current.replace(year=current.year + 1, month=1)
            else:
                current = current.replace(month=current.month + 1)
        
        # Equal allocation (20% each)
        allocation_per_fund = monthly_amount / 5
        
        # Track units and values
        fund_units = np.zeros(5)
        cash_flows = []
        portfolio_values = []
        total_invested = 0
        
        # Simulate SIP investments
        for date in sip_data.index:
            # Investment on SIP dates
            if date in sip_dates:
                nav_prices = sip_data.loc[date].values
                units_bought = allocation_per_fund / nav_prices
                fund_units += units_bought
                total_invested += monthly_amount
                cash_flows.append({'date': date, 'amount': -monthly_amount})
            
            # Calculate portfolio value
            current_navs = sip_data.loc[date].values
            portfolio_value = np.sum(fund_units * current_navs)
            
            portfolio_values.append({
                'date': date,
                'portfolio_value': portfolio_value,
                'total_invested': total_invested,
                'gain_loss': portfolio_value - total_invested
            })
        
        # Final cash flow for XIRR
        final_value = portfolio_values[-1]['portfolio_value']
        cash_flows.append({'date': sip_data.index[-1], 'amount': final_value})
        
        # Calculate XIRR
        xirr = self._calculate_xirr(cash_flows)
        
        # Store results
        results = {
            'fund_indices': fund_indices,
            'fund_names': selected_funds,
            'monthly_amount': monthly_amount,
            'total_invested': total_invested,
            'final_value': final_value,
            'absolute_return': final_value - total_invested,
            'return_pct': ((final_value - total_invested) / total_invested) * 100,
            'xirr': xirr,
            'num_sips': len(sip_dates),
            'start_date': start_date,
            'end_date': end_date,
            'portfolio_values': pd.DataFrame(portfolio_values).set_index('date'),
            'cash_flows': cash_flows
        }
        
        return results
    
    def _calculate_xirr(self, cash_flows, guess=0.1):
        """Calculate XIRR from cash flows"""
        dates = [cf['date'] for cf in cash_flows]
        amounts = [cf['amount'] for cf in cash_flows]
        
        if len(dates) < 2:
            return 0
        
        # Convert to days from first date
        base_date = dates[0]
        days = [(d - base_date).days for d in dates]
        
        def xirr_func(rate):
            return sum(amt / (1 + rate) ** (day / 365.0) for amt, day in zip(amounts, days))
        
        # Newton-Raphson method
        rate = guess
        for _ in range(100):
            try:
                f_val = xirr_func(rate)
                if abs(f_val) < 1e-6:
                    break
                
                # Derivative
                f_deriv = sum(-amt * (day / 365.0) / (1 + rate) ** (day / 365.0 + 1) 
                             for amt, day in zip(amounts, days))
                
                if abs(f_deriv) < 1e-6:
                    break
                    
                new_rate = rate - f_val / f_deriv
                if abs(new_rate - rate) < 1e-6:
                    rate = new_rate
                    break
                rate = new_rate
                
            except:
                return 0
        
        return rate * 100  # Convert to percentage
    
    def compare_portfolios(self, portfolio_list, monthly_amount=100000, start_date=None, end_date=None):
        """
        Compare multiple SIP portfolios
        
        Parameters:
        portfolio_list (list): List of fund index combinations
                              Example: [[0,1,2,3,4], [5,6,7,8,9], [10,11,12,13,14]]
        
        Returns:
        DataFrame: Comparison results sorted by XIRR
        """
        results = []
        
        for i, fund_indices in enumerate(portfolio_list):
            try:
                sip_result = self.simulate_sip(fund_indices, monthly_amount, start_date, end_date)
                
                # Create fund names string
                fund_names = ', '.join([name[:15] + '...' if len(name) > 15 else name 
                                      for name in sip_result['fund_names']])
                
                results.append({
                    'Portfolio': f'Portfolio_{i+1}',
                    'Fund_Indices': str(fund_indices),
                    'XIRR_Pct': round(sip_result['xirr'], 2),
                    'Total_Invested': sip_result['total_invested'],
                    'Final_Value': sip_result['final_value'],
                    'Absolute_Return': sip_result['absolute_return'],
                    'Return_Pct': round(sip_result['return_pct'], 2),
                    'Num_SIPs': sip_result['num_sips'],
                    'Fund_Names': fund_names
                })
                
            except Exception as e:
                print(f"❌ Error with portfolio {i+1}: {e}")
                continue
        
        # Create comparison DataFrame
        comparison_df = pd.DataFrame(results)
        
        if len(comparison_df) > 0:
            # Sort by XIRR descending
            comparison_df = comparison_df.sort_values('XIRR_Pct', ascending=False).reset_index(drop=True)
            comparison_df['Rank'] = range(1, len(comparison_df) + 1)
        
        return comparison_df
    
    def plot_sip_performance(self, sip_result, figsize=(15, 10)):
        """Plot SIP performance charts"""
        fig, axes = plt.subplots(2, 2, figsize=figsize)
        
        portfolio_df = sip_result['portfolio_values']
        
        # 1. Investment vs Portfolio Value
        axes[0, 0].plot(portfolio_df.index, portfolio_df['total_invested'], 
                       label='Total Invested', color='blue', linewidth=2)
        axes[0, 0].plot(portfolio_df.index, portfolio_df['portfolio_value'], 
                       label='Portfolio Value', color='green', linewidth=2)
        axes[0, 0].fill_between(portfolio_df.index, portfolio_df['total_invested'], 
                               portfolio_df['portfolio_value'], 
                               where=(portfolio_df['portfolio_value'] >= portfolio_df['total_invested']),
                               color='green', alpha=0.3)
        axes[0, 0].set_title('SIP: Investment vs Portfolio Value', fontweight='bold')
        axes[0, 0].set_ylabel('Amount (₹)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Gains/Losses
        axes[0, 1].plot(portfolio_df.index, portfolio_df['gain_loss'], color='purple', linewidth=2)
        axes[0, 1].fill_between(portfolio_df.index, 0, portfolio_df['gain_loss'], 
                               where=(portfolio_df['gain_loss'] >= 0), color='green', alpha=0.3)
        axes[0, 1].fill_between(portfolio_df.index, 0, portfolio_df['gain_loss'], 
                               where=(portfolio_df['gain_loss'] < 0), color='red', alpha=0.3)
        axes[0, 1].axhline(y=0, color='black', linestyle='-', alpha=0.5)
        axes[0, 1].set_title('Unrealized Gains/Losses', fontweight='bold')
        axes[0, 1].set_ylabel('P&L (₹)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Fund allocation pie chart
        fund_names = [name[:15] + '...' if len(name) > 15 else name for name in sip_result['fund_names']]
        axes[1, 0].pie([20, 20, 20, 20, 20], labels=fund_names, autopct='%1.0f%%', startangle=90)
        axes[1, 0].set_title('Equal Weight Allocation', fontweight='bold')
        
        # 4. Performance summary
        axes[1, 1].axis('off')
        summary_text = f"""
📊 SIP PERFORMANCE SUMMARY
{'='*30}
💰 Total Invested: ₹{sip_result['total_invested']:,.0f}
💎 Final Value: ₹{sip_result['final_value']:,.0f}
📈 Absolute Return: ₹{sip_result['absolute_return']:,.0f}
📊 Return %: {sip_result['return_pct']:.1f}%
🎯 XIRR: {sip_result['xirr']:.2f}%
🔄 Number of SIPs: {sip_result['num_sips']}
💵 Monthly SIP: ₹{sip_result['monthly_amount']:,.0f}
        """
        axes[1, 1].text(0.1, 0.9, summary_text, transform=axes[1, 1].transAxes, 
                        fontsize=12, verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        plt.show()
    
    def plot_comparison(self, comparison_df, figsize=(12, 8)):
        """Plot portfolio comparison"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)
        
        # XIRR comparison
        colors = plt.cm.viridis(np.linspace(0, 1, len(comparison_df)))
        bars = ax1.bar(range(len(comparison_df)), comparison_df['XIRR_Pct'], color=colors)
        ax1.set_title('XIRR Comparison', fontweight='bold')
        ax1.set_ylabel('XIRR (%)')
        ax1.set_xlabel('Portfolio Rank')
        ax1.set_xticks(range(len(comparison_df)))
        ax1.set_xticklabels([f'#{i+1}' for i in range(len(comparison_df))])
        ax1.grid(True, alpha=0.3)
        
        # Add value labels
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{height:.1f}%', ha='center', va='bottom')
        
        # Return vs Investment scatter
        scatter = ax2.scatter(comparison_df['Total_Invested'], comparison_df['Final_Value'], 
                            c=comparison_df['XIRR_Pct'], cmap='viridis', s=100, alpha=0.7)
        
        # Break-even line
        min_val = comparison_df['Total_Invested'].min()
        max_val = comparison_df['Final_Value'].max()
        ax2.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.5, label='Break-even')
        
        ax2.set_title('Final Value vs Investment', fontweight='bold')
        ax2.set_xlabel('Total Invested (₹)')
        ax2.set_ylabel('Final Value (₹)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.colorbar(scatter, ax=ax2, label='XIRR (%)')
        plt.tight_layout()
        plt.show()

# Initialize analyzer
sip = SIPAnalyzer(df)
print("\n🚀 SIP Analyzer ready for use!")

# 🎯 FRAMEWORK PRINCIPLES - CONFIRMED
print("\n" + "="*60)
print("🎯 SIP FRAMEWORK PRINCIPLES - STRICTLY ENFORCED")
print("="*60)
print("\n✅ CONFIRMED FEATURES:")
print("   🚫 NO WEIGHT OPTIMIZATION - Always equal 20% allocation")
print("   🚫 NO REBALANCING - Fixed portfolio over entire lifetime")
print("   💰 FIXED MONTHLY SIP - Same amount invested every month")
print("   🎯 XIRR ONLY - Single primary performance metric")
print("   ⚖️  EQUAL WEIGHTS - Exactly 20% in each of 5 funds")
print("\n❌ REMOVED COMPLEXITIES:")
print("   ❌ No portfolio optimization algorithms")
print("   ❌ No custom weight allocation options")
print("   ❌ No rebalancing logic or triggers")
print("   ❌ No multiple performance metrics")
print("   ❌ No risk-adjusted calculations")
print("\n🎯 SIMPLE WORKFLOW:")
print("   1️⃣  Select 5 fund indices")
print("   2️⃣  Set monthly SIP amount")
print("   3️⃣  Run simulation (equal weights, no rebalancing)")
print("   4️⃣  Get XIRR result")
print("   5️⃣  Compare different combinations")
print("\n🚀 READY FOR SIP ANALYSIS!")
print("="*60)

# Step 0: Verify framework principles
sip.verify_principles()

# Step 1: Show all available funds
sip.show_funds()

# Step 2: Analyze a single SIP portfolio
print("🔄 Running SIP analysis for Portfolio 1...")

# Select 5 funds (change these indices based on your preference)
selected_funds = [0, 5, 10, 15, 20]  # Equal-weighted: 20% each

# Run SIP simulation
result = sip.simulate_sip(
    fund_indices=selected_funds,
    monthly_amount=100000,      # ₹1 Lakh per month
    start_date='2010-01-01',    # Adjust based on your data
    end_date='2024-12-31'       # Adjust based on your data
)

print(f"\n✅ SIP Analysis Complete!")
print(f"🎯 XIRR: {result['xirr']:.2f}%")
print(f"💰 Total Invested: ₹{result['total_invested']:,.0f}")
print(f"💎 Final Value: ₹{result['final_value']:,.0f}")
print(f"📈 Absolute Return: ₹{result['absolute_return']:,.0f}")
print(f"📊 Return %: {result['return_pct']:.1f}%")
print(f"🔄 Number of SIPs: {result['num_sips']}")

print("\n📋 Selected Funds (Equal 20% allocation):")
for i, fund_name in enumerate(result['fund_names']):
    print(f"  {i+1}. {fund_name}")

# Step 3: Visualize the SIP performance
sip.plot_sip_performance(result)

# Step 4: Compare multiple SIP portfolios
print("🔄 Comparing multiple SIP portfolios...")

# Define different portfolio combinations (each with 5 funds)
portfolio_combinations = [
    [0, 1, 2, 3, 4],      # Portfolio 1: First 5 funds
    [5, 6, 7, 8, 9],      # Portfolio 2: Next 5 funds
    [10, 11, 12, 13, 14], # Portfolio 3: Next 5 funds
    [15, 16, 17, 18, 19], # Portfolio 4: Next 5 funds
    [20, 21, 22, 23, 24], # Portfolio 5: Next 5 funds
    [0, 5, 10, 15, 20],   # Portfolio 6: Mixed selection
    [2, 7, 12, 17, 22],   # Portfolio 7: Another mix
    [1, 6, 11, 16, 21],   # Portfolio 8: Another mix
]

# Run comparison
comparison = sip.compare_portfolios(
    portfolio_list=portfolio_combinations,
    monthly_amount=100000,
    start_date='2010-01-01',
    end_date='2023-12-31'
)

print(f"\n📊 Compared {len(comparison)} SIP portfolios")
print("\n🏆 Results (sorted by XIRR):")
print(comparison[['Rank', 'Portfolio', 'XIRR_Pct', 'Return_Pct', 'Final_Value']].to_string(index=False))

# Step 5: Visualize portfolio comparison
sip.plot_comparison(comparison)

# Step 6: Display ALL portfolio results in detail
print("🏆 ALL SIP PORTFOLIO RESULTS (Sorted by XIRR):")
print("=" * 70)

# Display all portfolios, not just top 3
for idx, row in comparison.iterrows():
    print(f"\n#{row['Rank']} {row['Portfolio']}:")
    print(f"  🎯 XIRR: {row['XIRR_Pct']:.2f}%")
    print(f"  💰 Total Invested: ₹{row['Total_Invested']:,.0f}")
    print(f"  💎 Final Value: ₹{row['Final_Value']:,.0f}")
    print(f"  📈 Absolute Return: ₹{row['Absolute_Return']:,.0f}")
    print(f"  📊 Return %: {row['Return_Pct']:.1f}%")
    print(f"  📋 Fund Indices: {row['Fund_Indices']}")
    print(f"  💼 Funds: {row['Fund_Names']}")
    print("-" * 50)  # Add separator between portfolios

print(f"\n📊 XIRR Statistics:")
print(f"  🏆 Best XIRR: {comparison['XIRR_Pct'].max():.2f}%")
print(f"  📉 Worst XIRR: {comparison['XIRR_Pct'].min():.2f}%")
print(f"  📊 Average XIRR: {comparison['XIRR_Pct'].mean():.2f}%")
print(f"  📈 XIRR Range: {comparison['XIRR_Pct'].max() - comparison['XIRR_Pct'].min():.2f}%")

# Step 7: Custom portfolio analysis
print("🎯 CUSTOM PORTFOLIO ANALYSIS")
print("=" * 40)

# You can easily test any combination of 5 funds
custom_funds = [3, 8, 13, 18, 23]  # Change these indices as needed

print(f"Testing fund indices: {custom_funds}")

custom_result = sip.simulate_sip(
    fund_indices=custom_funds,
    monthly_amount=100000,
    start_date='2010-01-01',
    end_date='2023-12-31'
)

print(f"\n✅ Custom Portfolio Results:")
print(f"🎯 XIRR: {custom_result['xirr']:.2f}%")
print(f"💰 Total Invested: ₹{custom_result['total_invested']:,.0f}")
print(f"💎 Final Value: ₹{custom_result['final_value']:,.0f}")
print(f"📈 Absolute Return: ₹{custom_result['absolute_return']:,.0f}")
print(f"📊 Return %: {custom_result['return_pct']:.1f}%")

print("\n📋 Fund Details:")
for i, fund_name in enumerate(custom_result['fund_names']):
    print(f"  {i+1}. {fund_name} (20% allocation)")

# Compare with best performer
best_xirr = comparison['XIRR_Pct'].max()
performance_vs_best = custom_result['xirr'] - best_xirr

if performance_vs_best > 0:
    print(f"\n🎉 This portfolio outperforms the best by {performance_vs_best:.2f}%!")
elif performance_vs_best < -1:
    print(f"\n📉 This portfolio underperforms the best by {abs(performance_vs_best):.2f}%")
else:
    print(f"\n📊 This portfolio performs similarly to the best (difference: {performance_vs_best:.2f}%)")