{"cells": [{"cell_type": "code", "execution_count": null, "id": "1c2bdd6c-822c-456d-8cfe-59783a0d5c75", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from dateutil.relativedelta import relativedelta\n", "import numpy as np\n", "import re\n", "import matplotlib.pyplot as plt\n", "from matplotlib import pyplot as plt\n", "from matplotlib.pyplot import figure\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "import math\n", "import os\n", "from datetime import date, timedelta, datetime\n", "import time\n", "from tqdm import tqdm\n", "import pyodbc\n", "import seaborn as sns\n", "from scipy import stats\n", "import xlsxwriter\n", "from matplotlib.ticker import MaxNLocator\n", "from matplotlib.backends.backend_pdf import PdfPages\n", "import itertools\n", "from data import Data\n", "d = Data()\n", "import numpy_financial as npf\n", "from scipy.optimize import newton, brentq\n", "start_time = time.perf_counter()"]}, {"cell_type": "code", "execution_count": 3, "id": "5f007dc0", "metadata": {}, "outputs": [], "source": ["madp_master_data = pd.read_excel(r\"\\\\*************\\c$\\Users\\Administrator\\PycharmProjects\\Projects\\MADP-ALPHA\\madp_data_bb_things\\MADP-Master-Data.xlsx\")\n", "india_gold = madp_master_data[['Date', 'Gold per ounce in INR']]\n", "gmdp = pd.read_excel(r'\\\\*************\\c$\\Users\\Administrator\\PycharmProjects\\Projects\\globalMacroDataPipeline\\Historic Macro Data2.xlsx')"]}, {"cell_type": "code", "execution_count": 5, "id": "794ba79e-6ef1-413d-a9b2-39281181736e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3678, 25)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Nifty 500 Value 50</th>\n", "      <th>Nifty MidSmallcap400 Momentum Quality 100</th>\n", "      <th>Nifty 500 Momentum 50</th>\n", "      <th>Nifty Alpha Low Vol 30</th>\n", "      <th>Nifty 200 Momentum 30</th>\n", "      <th>Nifty 100 Quality 30</th>\n", "      <th>Nifty 200 Quality 30</th>\n", "      <th>Nifty 100 Equal Weight</th>\n", "      <th>Nifty Microcap 250</th>\n", "      <th>Nifty Smallcap 250</th>\n", "      <th>...</th>\n", "      <th>Nifty 50</th>\n", "      <th>Nifty 100</th>\n", "      <th>Nifty Midcap 150</th>\n", "      <th>Nifty Midcap 100</th>\n", "      <th>Nifty 500 Equal Weight</th>\n", "      <th>Nifty LargeMidCap 250</th>\n", "      <th>NIFTY DIVIDEND OPPORTUNITIES 50</th>\n", "      <th>Nifty 200 Value 30</th>\n", "      <th>Gold per ounce in INR</th>\n", "      <th>India 10Y</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-08-26</th>\n", "      <td>15338.11</td>\n", "      <td>54573.31</td>\n", "      <td>62695.55</td>\n", "      <td>30045.20</td>\n", "      <td>36719.60</td>\n", "      <td>6077.30</td>\n", "      <td>22681.85</td>\n", "      <td>34297.00</td>\n", "      <td>25082.10</td>\n", "      <td>18081.15</td>\n", "      <td>...</td>\n", "      <td>25010.60</td>\n", "      <td>26076.80</td>\n", "      <td>21758.45</td>\n", "      <td>58931.15</td>\n", "      <td>15039.06</td>\n", "      <td>16443.65</td>\n", "      <td>6845.85</td>\n", "      <td>14124.12</td>\n", "      <td>211230.8438</td>\n", "      <td>6.851</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-27</th>\n", "      <td>15381.85</td>\n", "      <td>54843.62</td>\n", "      <td>62820.86</td>\n", "      <td>30027.60</td>\n", "      <td>36717.65</td>\n", "      <td>6052.10</td>\n", "      <td>22572.95</td>\n", "      <td>34316.05</td>\n", "      <td>25179.55</td>\n", "      <td>18219.55</td>\n", "      <td>...</td>\n", "      <td>25017.75</td>\n", "      <td>26083.15</td>\n", "      <td>21869.75</td>\n", "      <td>59220.25</td>\n", "      <td>15009.25</td>\n", "      <td>16487.70</td>\n", "      <td>6841.25</td>\n", "      <td>14152.44</td>\n", "      <td>211245.3438</td>\n", "      <td>6.861</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-28</th>\n", "      <td>15337.64</td>\n", "      <td>54779.49</td>\n", "      <td>63088.08</td>\n", "      <td>30041.95</td>\n", "      <td>36847.95</td>\n", "      <td>6065.30</td>\n", "      <td>22625.35</td>\n", "      <td>34354.35</td>\n", "      <td>25104.90</td>\n", "      <td>18180.30</td>\n", "      <td>...</td>\n", "      <td>25052.35</td>\n", "      <td>26116.80</td>\n", "      <td>21838.70</td>\n", "      <td>59146.40</td>\n", "      <td>14943.41</td>\n", "      <td>16486.55</td>\n", "      <td>6847.20</td>\n", "      <td>14116.77</td>\n", "      <td>210544.0000</td>\n", "      <td>6.861</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-29</th>\n", "      <td>15349.96</td>\n", "      <td>54498.30</td>\n", "      <td>62868.04</td>\n", "      <td>30133.80</td>\n", "      <td>36895.00</td>\n", "      <td>6086.15</td>\n", "      <td>22719.80</td>\n", "      <td>34379.35</td>\n", "      <td>24831.20</td>\n", "      <td>18077.05</td>\n", "      <td>...</td>\n", "      <td>25151.95</td>\n", "      <td>26188.90</td>\n", "      <td>21741.35</td>\n", "      <td>58883.95</td>\n", "      <td>15033.17</td>\n", "      <td>16472.55</td>\n", "      <td>6878.00</td>\n", "      <td>14160.31</td>\n", "      <td>211478.0000</td>\n", "      <td>6.864</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-30</th>\n", "      <td>15431.09</td>\n", "      <td>54826.74</td>\n", "      <td>63156.93</td>\n", "      <td>30322.50</td>\n", "      <td>37102.65</td>\n", "      <td>6103.70</td>\n", "      <td>22807.20</td>\n", "      <td>34533.15</td>\n", "      <td>25108.80</td>\n", "      <td>18170.25</td>\n", "      <td>...</td>\n", "      <td>25235.90</td>\n", "      <td>26274.35</td>\n", "      <td>21925.45</td>\n", "      <td>59286.65</td>\n", "      <td>15033.17</td>\n", "      <td>16568.95</td>\n", "      <td>6899.35</td>\n", "      <td>14225.24</td>\n", "      <td>209761.3438</td>\n", "      <td>6.863</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 25 columns</p>\n", "</div>"], "text/plain": ["            Nifty 500 Value 50  Nifty MidSmallcap400 Momentum Quality 100  \\\n", "Date                                                                        \n", "2024-08-26            15338.11                                   54573.31   \n", "2024-08-27            15381.85                                   54843.62   \n", "2024-08-28            15337.64                                   54779.49   \n", "2024-08-29            15349.96                                   54498.30   \n", "2024-08-30            15431.09                                   54826.74   \n", "\n", "            Nifty 500 Momentum 50  Nifty Alpha Low Vol 30  \\\n", "Date                                                        \n", "2024-08-26               62695.55                30045.20   \n", "2024-08-27               62820.86                30027.60   \n", "2024-08-28               63088.08                30041.95   \n", "2024-08-29               62868.04                30133.80   \n", "2024-08-30               63156.93                30322.50   \n", "\n", "            Nifty 200 Momentum 30  Nifty 100 Quality 30  Nifty 200 Quality 30  \\\n", "Date                                                                            \n", "2024-08-26               36719.60               6077.30              22681.85   \n", "2024-08-27               36717.65               6052.10              22572.95   \n", "2024-08-28               36847.95               6065.30              22625.35   \n", "2024-08-29               36895.00               6086.15              22719.80   \n", "2024-08-30               37102.65               6103.70              22807.20   \n", "\n", "            Nifty 100 Equal Weight  Nifty Microcap 250  Nifty Smallcap 250  \\\n", "Date                                                                         \n", "2024-08-26                34297.00            25082.10            18081.15   \n", "2024-08-27                34316.05            25179.55            18219.55   \n", "2024-08-28                34354.35            25104.90            18180.30   \n", "2024-08-29                34379.35            24831.20            18077.05   \n", "2024-08-30                34533.15            25108.80            18170.25   \n", "\n", "            ...  Nifty 50  Nifty 100  Nifty Midcap 150  Nifty Midcap 100  \\\n", "Date        ...                                                            \n", "2024-08-26  ...  25010.60   26076.80          21758.45          58931.15   \n", "2024-08-27  ...  25017.75   26083.15          21869.75          59220.25   \n", "2024-08-28  ...  25052.35   26116.80          21838.70          59146.40   \n", "2024-08-29  ...  25151.95   26188.90          21741.35          58883.95   \n", "2024-08-30  ...  25235.90   26274.35          21925.45          59286.65   \n", "\n", "            Nifty 500 Equal Weight  Nifty LargeMidCap 250  \\\n", "Date                                                        \n", "2024-08-26                15039.06               16443.65   \n", "2024-08-27                15009.25               16487.70   \n", "2024-08-28                14943.41               16486.55   \n", "2024-08-29                15033.17               16472.55   \n", "2024-08-30                15033.17               16568.95   \n", "\n", "            NIFTY DIVIDEND OPPORTUNITIES 50  Nifty 200 Value 30  \\\n", "Date                                                              \n", "2024-08-26                          6845.85            14124.12   \n", "2024-08-27                          6841.25            14152.44   \n", "2024-08-28                          6847.20            14116.77   \n", "2024-08-29                          6878.00            14160.31   \n", "2024-08-30                          6899.35            14225.24   \n", "\n", "            Gold per ounce in INR  India 10Y  \n", "Date                                          \n", "2024-08-26            211230.8438      6.851  \n", "2024-08-27            211245.3438      6.861  \n", "2024-08-28            210544.0000      6.861  \n", "2024-08-29            211478.0000      6.864  \n", "2024-08-30            209761.3438      6.863  \n", "\n", "[5 rows x 25 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# All Fund Indices Data\n", "df = pd.read_excel('./input_data/Fund Indices Data.xlsx').dropna()\n", "df.Date = pd.to_datetime(df.Date)\n", "df = df.sort_index(ascending=True)\n", "df = df.iloc[1:,:]\n", "\n", "df = pd.merge(df, india_gold, on='Date', how='left').merge(gmdp[['Date', 'India 10Y']], on='Date', how='left')\n", "df.index = pd.to_datetime(df.index)\n", "df.set_index('Date', inplace=True)\n", "df = df.dropna()\n", "print(df.shape)\n", "df.tail() \n"]}, {"cell_type": "markdown", "id": "52595771-696c-49b6-ac27-bcbda313b835", "metadata": {}, "source": ["# Total Portfolio NAV Calculation"]}, {"cell_type": "code", "execution_count": 6, "id": "22de7ed1-412e-4101-b24e-621a7d08c502", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["## List of SIP dates generated. ##\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Calculating NAV: 100%|██████████| 3678/3678 [00:00<00:00, 8885.69it/s] \n"]}], "source": ["conservative_folio = {\n", "    'Nifty LargeMidCap 250':20.0, \n", "    'Nifty 100 Quality 30':20.0,\n", "    'Nifty 500 Equal Weight':20.0,\n", "    'Nifty Alpha Low Vol 30':20.0,\n", "    'Nifty 500':20.0,\n", "   \n", "}\n", "\n", "moderate_folio = {\n", "    'Nifty 500 Value 50':20.0,\n", "    'Nifty MidSmallcap400 Momentum Quality 100':20.0,\n", "    \"Nifty 200 Quality 30\":20.0,\n", "    \"Nifty Alpha Low Vol 30\":20.0,\n", "    \"Nifty Smallcap 250\":20.0,\n", "   \n", "}\n", "\n", "\n", "aggresive_folio = {\n", "    'Nifty 500 Momentum 50':20.0,\n", "    'Nifty Microcap 250':20.0,\n", "    'Nifty Midcap 150':20.0,\n", "    \"Nifty MidSmallcap400 Momentum Quality 100\":20.0,\n", "    \"Nifty 500\":20.0,\n", "    \n", "}\n", "\n", "# ------------------------------------\n", "\n", "multiasset_conservative_folio = {\n", "    'Nifty LargeMidCap 250':10.0, \n", "    'Nifty 100 Quality 30':10.0,\n", "    'Nifty 500 Equal Weight':10.0,\n", "    'Nifty Alpha Low Vol 30':10.0,\n", "    'Nifty 500':10.0,\n", "    'GOLD(Ounce/INR)':25.0,\n", "    'India 10Y':25.0,\n", "}\n", "\n", "multiasset_moderate_folio = {\n", "    'Nifty 500 Value 50':13.0,\n", "    'Nifty MidSmallcap400 Momentum Quality 100':13.0,\n", "    \"Nifty 200 Quality 30\":13.0,\n", "    \"Nifty Alpha Low Vol 30\":13.0,\n", "    \"Nifty Smallcap 250\":13.0,\n", "    'GOLD(Ounce/INR)':17.5,\n", "    'India 10Y':17.5,\n", "}\n", "\n", "\n", "multiasset_aggresive_folio = {\n", "    'Nifty 500 Momentum 50':16.0,\n", "    'Nifty Microcap 250':16.0,\n", "    'Nifty Midcap 150':16.0,\n", "    \"Nifty MidSmallcap400 Momentum Quality 100\":16.0,\n", "    \"Nifty 500\":16.0,\n", "    'GOLD(Ounce/INR)':10.0,\n", "    'India 10Y':10.0,\n", "}\n", "\n", "bm1 = {\n", "    'Nifty 50': 100.0\n", "}\n", "\n", "bm2 = {\n", "    'Nifty 500': 100.0\n", "}\n", "\n", "# ----------------------------------------------\n", "# investor_style_folio = multiasset_conservative_folio    \n", "# investor_style_folio = multiasset_moderate_folio    \n", "investor_style_folio = bm2   \n", "# ----------------------------------------------\n", "\n", "####################################################\n", "# Normalize weights to ensure they sum to 100%\n", "####################################################\n", "\n", "portfolio_weights = {k: v / 100 for k, v in investor_style_folio.items()}\n", "component_navs = df.loc[:, investor_style_folio.keys()].copy()\n", "\n", "sip_dates = pd.date_range(start=component_navs.index[0], end=component_navs.index[-1], freq='MS') + pd.Timedelta(days=1)\n", "\n", "def validate_sip_dates(sip_dates, dataFrame):\n", "    rebalance_dates_adjusted = []\n", "    for date in sip_dates:\n", "        # Find the first valid date on or after the rebalance date\n", "        valid_date = dataFrame.index[dataFrame.index >= date][0]\n", "        rebalance_dates_adjusted.append(valid_date)\n", "        \n", "    if dataFrame.index[0] not in rebalance_dates_adjusted:\n", "        rebalance_dates_adjusted.insert(0, dataFrame.index[0])\n", "    \n", "    print(\"## List of SIP dates generated. ##\")\n", "    return rebalance_dates_adjusted\n", "\n", "sip_dates = validate_sip_dates(sip_dates, component_navs)\n", "\n", "units_df = pd.DataFrame(0, index=component_navs.index, columns=component_navs.columns)\n", "\n", "initial_corpus = 100000\n", "sip_amount = initial_corpus\n", "\n", "# Loop through all dates in the NAV data\n", "for i, date in enumerate(tqdm(component_navs.index, desc=\"Calculating NAV\")):\n", "    if date in sip_dates:\n", "        # Calculate units purchased on SIP dates\n", "        sip_units = {fund: (sip_amount * weight) / component_navs.loc[date, fund] \n", "                     for fund, weight in portfolio_weights.items()}\n", "        # Add new units to cumulative units\n", "        if i > 0:  # Ensure there's a previous day's units\n", "            units_df.loc[date] = units_df.iloc[i - 1] + pd.Series(sip_units)\n", "        else:  # First row, initialize with SIP units\n", "            units_df.loc[date] = pd.Series(sip_units)\n", "    else:\n", "        # For non-SIP dates, carry forward the previous day's units\n", "        if i > 0:  # Ensure there's a previous day\n", "            units_df.loc[date] = units_df.iloc[i - 1]\n", "\n", "\n", "sip_portfolio_value = (units_df * component_navs).sum(axis=1)\n", "sip_portfolio_value = sip_portfolio_value.to_frame(name='Total Portfolio Value')"]}, {"cell_type": "markdown", "id": "f1b9fe11-b88d-48a1-8623-b10b1d0bc889", "metadata": {}, "source": ["# XIRR Calculation"]}, {"cell_type": "code", "execution_count": 7, "id": "96d0ba37-d136-4f4f-83b7-8bab55f74381", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["XIRR: 14.93%\n"]}], "source": ["# Step 1: Create cash flows\n", "cash_flows = []\n", "\n", "# Add SIP amounts as negative cash flows\n", "for date in sip_dates:\n", "    cash_flows.append({'date': date, 'value': -sip_amount})\n", "\n", "# Add the final portfolio value as a positive cash flow\n", "final_value = sip_portfolio_value.squeeze().iloc[-1]\n", "cash_flows.append({'date': sip_portfolio_value.squeeze().index[-1], 'value': final_value})\n", "\n", "# Convert to DataFrame for easier manipulation\n", "cash_flow_df = pd.DataFrame(cash_flows)\n", "\n", "# Step 2: Calculate XIRR function\n", "def xirr(cash_flows, guess=0.1):\n", "    dates = pd.to_datetime(cash_flows['date'])\n", "    values = cash_flows['value']\n", "    years = (dates - dates.iloc[0]).dt.days / 365\n", "\n", "    def xirr_equation(rate):\n", "        return np.sum(values / (1 + rate) ** years)\n", "\n", "    try:\n", "        return newton(xirr_equation, guess)\n", "    except (RuntimeError, OverflowError):\n", "        return np.nan \n", "\n", "# Step 3: Compute XIRR\n", "calculated_xirr = xirr(cash_flow_df)\n", "print(f\"XIRR: {calculated_xirr:.2%}\")"]}, {"cell_type": "markdown", "id": "759300e3-554b-43c7-9b94-63859dbb63e7", "metadata": {}, "source": ["# Non-SIP Folio for Statistical Summary"]}, {"cell_type": "code", "execution_count": 8, "id": "8b741163-db8a-4d4d-81cb-6e248be373c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["## List of Quarter-End dates generated. ##\n"]}], "source": ["# Calculate initial units using the initial corpus\n", "initial_units = {fund: (initial_corpus * weight) / component_navs.iloc[0][fund] \n", "                 for fund, weight in portfolio_weights.items()}\n", "\n", "# Convert to a DataFrame with constant units for all dates\n", "pseudo_units_df = pd.DataFrame(initial_units, index=component_navs.index)\n", "\n", "# Calculate pseudo-portfolio value\n", "non_sip_portfolio_value = (pseudo_units_df * component_navs).sum(axis=1)\n", "\n", "# Convert to a DataFrame for easier handling\n", "non_sip_portfolio_value = non_sip_portfolio_value.to_frame(name='Non-SIP Portfolio Value')\n", "\n", "non_sip_portfolio_value['dailyPctChange'] = non_sip_portfolio_value['Non-SIP Portfolio Value'].pct_change()\n", "\n", "def validate_quarter_end_dates(sip_dates, dataFrame):\n", "    rebalance_dates_adjusted = []\n", "    for date in sip_dates:\n", "        # Find the first valid date on or after the rebalance date\n", "        valid_date = dataFrame.index[dataFrame.index <= date][-1]\n", "        rebalance_dates_adjusted.append(valid_date)\n", "    print(\"## List of Quarter-End dates generated. ##\")\n", "    return rebalance_dates_adjusted\n", "\n", "# Tracking error pct\n", "annual_tracking_error = 0.015\n", "\n", "rbdate_counter = 0\n", "for date in validate_quarter_end_dates(non_sip_portfolio_value.resample('QE').last().index, component_navs)[:-1]:\n", "    rbdate_counter +=1\n", "    non_sip_portfolio_value.loc[date, 'dailyPctChange'] -= annual_tracking_error/4\n", "\n", "non_sip_portfolio_value['Non-SIP Portfolio Value (After Tracking Error)'] = (1 + non_sip_portfolio_value['dailyPctChange']).cumprod()\n", "non_sip_portfolio_value['Non-SIP Portfolio Value (After Tracking Error)'] = non_sip_portfolio_value['Non-SIP Portfolio Value (After Tracking Error)']/non_sip_portfolio_value['Non-SIP Portfolio Value (After Tracking Error)'].dropna().iloc[0] * initial_corpus\n", "# non_sip_portfolio_value.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "04f8c3b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["## Saved Charts ##\n"]}], "source": ["# roll_3y_cagr = (df['Nifty 500']\n", "#                 .pct_change(252*3)\n", "#                 .add(1)\n", "#                 .pow(1/3)\n", "#                 .sub(1)\n", "#                 .mul(100))\n", "\n", "# roll_5y_cagr = (df['Nifty 500']\n", "#                 .pct_change(252*5)\n", "#                 .add(1)\n", "#                 .pow(1/5)\n", "#                 .sub(1)\n", "#                 .mul(100))\n", "\n", "# roll_3y_cagr.to_excel('./msukaanPPTCharts/Nifty 500 -3Y Rolling CAGR.xlsx')\n", "# roll_5y_cagr.to_excel('./msukaanPPTCharts/Nifty 500 -5Y Rolling CAGR.xlsx')\n", "# print('## Saved Charts ##')"]}, {"cell_type": "markdown", "id": "156cd325-ada5-42e7-91b6-d58101932762", "metadata": {}, "source": ["# Key Performance Metrics"]}, {"cell_type": "code", "execution_count": 9, "id": "1d4df190-fe6e-4973-b161-720e67f23827", "metadata": {}, "outputs": [], "source": ["class FinancialMetrics:\n", "    \"\"\"\n", "    A comprehensive class for analyzing drawdowns and calculating key performance metrics.\n", "    \"\"\"\n", "    \n", "    def __init__(self, nav_df):\n", "        \"\"\"\n", "        Initialize the FinancialMetrics class with NAV data.\n", "        \n", "        Parameters:\n", "        nav_df (pd.DataFrame or pd.Series): DataFrame or Series containing NAV data.\n", "        \"\"\"\n", "        self.nav_series = nav_df.squeeze()  \n", "        self.metrics = {}\n", "        \n", "        # Handle insufficient data\n", "        if len(self.nav_series) < 252:\n", "            self.metrics = {metric: np.nan for metric in [\n", "                \"CAGR\", \"MDD\", \"Avg_Top_5_DD\", \"Volatility\", \"Sharpe\", \n", "                \"Sortino\", \"Rolling_1Y_Mean_Return\", \"Rolling_3Y_Mean_Return\",\n", "                \"Rolling_3Y_Volatility\", \"Closing NAV\"]}\n", "            return\n", "        \n", "        # Drop NaN values for reliable calculations\n", "        self.nav_series = self.nav_series.dropna()\n", "    \n", "    # ========================= Key Performance Metrics =========================\n", "    def calculate_cagr(self):\n", "        \"\"\"Calculate Compound Annual Growth Rate (CAGR).\"\"\"\n", "        start_nav = self.nav_series.iloc[0]\n", "        end_nav = self.nav_series.iloc[-1]\n", "        num_years = (len(self.nav_series)) / 252\n", "        self.metrics[\"CAGR\"] = (end_nav / start_nav) ** (1 / num_years) - 1 if num_years > 0 else np.nan\n", "    \n", "    def calculate_sharpe_ratio(self, risk_free_rate=0.0):\n", "        \"\"\"Calculate Sharpe Ratio.\"\"\"\n", "        returns = self.nav_series.pct_change().dropna()\n", "        annual_return = returns.mean() * 252\n", "        annual_volatility = returns.std() * np.sqrt(252)\n", "        self.metrics[\"Sharpe\"] = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else np.nan\n", "    \n", "    def calculate_sortino_ratio(self, risk_free_rate=0.0):\n", "        \"\"\"Calculate Sortino Ratio.\"\"\"\n", "        returns = self.nav_series.pct_change().dropna()\n", "        annual_return = returns.mean() * 252\n", "        downside_returns = returns[returns < 0]\n", "        downside_deviation = downside_returns.std() * np.sqrt(252)\n", "        self.metrics[\"Sortino\"] = (annual_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else np.nan\n", "\n", "    def calculate_rolling_metrics(self, window=252):\n", "        \"\"\"Calculate rolling metrics.\"\"\"\n", "        # Rolling 1-year mean return\n", "        daily_return_series = self.nav_series.pct_change().dropna()\n", "        return_series = self.nav_series.pct_change(window).dropna() \n", "        self.metrics[\"Rolling_1Y_Mean_Return\"] = return_series.mean()\n", "    \n", "        # Rolling 1-year volatility\n", "        rolling_volatility = daily_return_series.rolling(window).std() * np.sqrt(window)\n", "        self.metrics[\"Rolling_1Y_Volatility\"] = rolling_volatility.iloc[-1] if not rolling_volatility.empty else np.nan\n", "        \n", "        # Rolling 3-year mean return\n", "        return_series = self.nav_series.pct_change(window*3).dropna()\n", "        self.metrics[\"Rolling_3Y_Mean_Return\"] = return_series.mean()\n", "\n", "        # Rolling 5-year mean return\n", "        return_series = self.nav_series.pct_change(window*5).dropna() \n", "        self.metrics[\"Rolling_5Y_Mean_Return\"] = return_series.mean()\n", "\n", "        # Rolling 3-year volatility\n", "        rolling_volatility = daily_return_series.rolling(window*3).std() * np.sqrt(window)\n", "        self.metrics[\"Rolling_3Y_Volatility\"] = rolling_volatility.iloc[-1]  if not rolling_volatility.empty else np.nan\n", "\n", "\n", "    def calculate_drawdowns(self):\n", "        \"\"\"Calculate Maximum Drawdown (MDD) and Mean of Top 5 Drawdowns.\"\"\"\n", "        def to_drawdown_series(prices):\n", "            return prices / np.maximum.accumulate(prices) - 1.0\n", "\n", "        def drawdown_details(drawdown):\n", "            starts, ends = [], []\n", "            for i, value in enumerate(drawdown):\n", "                if i > 0 and value == 0 and drawdown[i - 1] < 0:\n", "                    ends.append(i - 1)\n", "                if value < 0 and (i == 0 or drawdown[i - 1] == 0):\n", "                    starts.append(i)\n", "            if len(starts) > len(ends):\n", "                ends.append(len(drawdown) - 1)\n", "            drawdown_data = []\n", "            for start, end in zip(starts, ends):\n", "                drawdown_data.append((start, drawdown[start:end + 1].idxmin(), end, drawdown[start:end + 1].min()))\n", "            return pd.DataFrame(drawdown_data, columns=['Start', 'Valley', 'End', 'Drawdown'])\n", "\n", "        nav_series = self.nav_series\n", "        drawdown = to_drawdown_series(nav_series)\n", "        details = drawdown_details(drawdown)\n", "        if not details.empty:\n", "            self.metrics[\"MDD\"] = details['Drawdown'].min() \n", "            self.metrics[\"Avg_Top_5_DD\"] = details.nsmallest(5, 'Drawdown')['Drawdown'].mean() \n", "            self.metrics[\"Avg_Top_10_DD\"] = details.nsmallest(10, 'Drawdown')['Drawdown'].mean() \n", "        else:\n", "            self.metrics[\"MDD\"] = np.nan\n", "            self.metrics[\"Avg_Top_5_DD\"] = np.nan\n", "\n", "    def generate_metrics(self):\n", "        \"\"\"Calculate all key performance metrics.\"\"\"\n", "        self.calculate_cagr()\n", "        self.calculate_drawdowns()\n", "        self.calculate_sharpe_ratio()\n", "        self.calculate_sortino_ratio()\n", "        self.calculate_rolling_metrics()\n", "        return self.metrics\n"]}, {"cell_type": "code", "execution_count": 10, "id": "ae233676-7998-46e3-89fd-833b24452378", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-------------------------\n", "XIRR: 14.9271%\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CAGR</th>\n", "      <th>MDD</th>\n", "      <th>Avg_Top_5_DD</th>\n", "      <th>Avg_Top_10_DD</th>\n", "      <th><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Rolling_1Y_Mean_Return</th>\n", "      <th>Rolling_1Y_Volatility</th>\n", "      <th>Rolling_3Y_Mean_Return</th>\n", "      <th>Rolling_5Y_Mean_Return</th>\n", "      <th>Rolling_3Y_Volatility</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.117251</td>\n", "      <td>-0.384399</td>\n", "      <td>-0.241506</td>\n", "      <td>-0.174847</td>\n", "      <td>0.742484</td>\n", "      <td>0.969319</td>\n", "      <td>0.122571</td>\n", "      <td>0.131867</td>\n", "      <td>0.403892</td>\n", "      <td>0.719889</td>\n", "      <td>0.141376</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       CAGR       MDD  Avg_Top_5_DD  Avg_Top_10_DD    <PERSON>   Sortino  \\\n", "0  0.117251 -0.384399     -0.241506      -0.174847  0.742484  0.969319   \n", "\n", "   Rolling_1Y_Mean_Return  Rolling_1Y_Volatility  Rolling_3Y_Mean_Return  \\\n", "0                0.122571               0.131867                0.403892   \n", "\n", "   Rolling_5Y_Mean_Return  Rolling_3Y_Volatility  \n", "0                0.719889               0.141376  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["fm = FinancialMetrics(df[['Nifty 50']])\n", "\n", "# print()\n", "print(\"-------------------------\")\n", "print(f\"XIRR: {calculated_xirr:.4%}\")\n", "pd.DataFrame([fm.generate_metrics()])\n"]}, {"cell_type": "code", "execution_count": null, "id": "8b1e287a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}