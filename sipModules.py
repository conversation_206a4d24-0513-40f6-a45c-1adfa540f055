import pandas as pd
import numpy as np
from scipy.optimize import newton
import math

## --- Validate SIP Dates --- ##
def validate_sip_dates(sip_dates, dataFrame):
    rebalance_dates_adjusted = []
    for date in sip_dates:
        # Find the first valid date on or after the rebalance date
        valid_date = dataFrame.index[dataFrame.index >= date][0]
        rebalance_dates_adjusted.append(valid_date)
        
    if dataFrame.index[0] not in rebalance_dates_adjusted:
        rebalance_dates_adjusted.insert(0, dataFrame.index[0])
    
    print("## List of SIP dates generated. ##")
    return rebalance_dates_adjusted


## --- SIP Units and Nav --- ##
class SIPAllocator:
    def __init__(self, component_navs: pd.DataFrame, sip_dates: list, weights: dict, monthly_investment: float):
        """
        Initializes the SIPAllocator.
        
        Args:
            component_navs (pd.DataFrame): NAV data for each fund. Index should be date, columns are fund names.
            sip_dates (list): List of SIP dates (datetime.date or pd.Timestamp).
            weights (dict): Fund weight allocations (sum should be 1).
            monthly_investment (float): Amount invested each SIP date.
        """
        self.navs = component_navs
        self.sip_dates = sip_dates
        self.weights = {k: v for k, v in weights.items() if v > 0}
        self.monthly_investment = monthly_investment
        self.units_df = pd.DataFrame(0, index=self.navs.index, columns=self.navs.columns)

    def allocate_units(self):
        """
        Allocates SIP units across dates and funds.
        Returns:
            pd.DataFrame: A DataFrame of accumulated units over time.
        """
        for i, date in enumerate(self.navs.index):
            if date in self.sip_dates:
                sip_units = {
                    fund: (self.monthly_investment * self.weights[fund]) / self.navs.loc[date, fund]
                    for fund in self.weights
                }
                if i > 0:
                    self.units_df.loc[date] = self.units_df.iloc[i - 1] + pd.Series(sip_units)
                else:
                    self.units_df.loc[date] = pd.Series(sip_units)
            else:
                if i > 0:
                    self.units_df.loc[date] = self.units_df.iloc[i - 1]
        return self.navs, self.weights, self.units_df


## --- XIRR Calculator --- ##
class XIRRCalculator:
    """
    Class to calculate XIRR (Extended Internal Rate of Return) based on a series of cash flows.
    """
    
    def __init__(self, cash_flows):
        """
        Initialize the calculator with a list or DataFrame of cash flows.
        
        Parameters:
        cash_flows (list[dict] or pd.DataFrame): Each cash flow must contain 'date' and 'value'.
        """
        if isinstance(cash_flows, list):
            self.cash_flows = pd.DataFrame(cash_flows)
        else:
            self.cash_flows = cash_flows.copy()
        
        self.cash_flows['date'] = pd.to_datetime(self.cash_flows['date'])
        self.cash_flows.sort_values(by='date', inplace=True)
        self.xirr_result = None

    def _calculate_xirr(self, guess=0.1):
        """
        Private method to compute XIRR using Newton-Raphson method.
        
        Parameters:
        guess (float): Initial guess for XIRR value.
        
        Returns:
        float: XIRR value
        """
        dates = self.cash_flows['date']
        values = self.cash_flows['value']
        years = (dates - dates.iloc[0]).dt.days / 365.0

        def xirr_equation(rate):
            return np.sum(values / (1 + rate) ** years)

        return newton(xirr_equation, guess)

    def compute(self, guess=0.1):
        """
        Public method to trigger XIRR calculation.
        
        Parameters:
        guess (float): Initial guess for Newton's method.

        Returns:
        float: Computed XIRR
        """
        self.xirr_result = self._calculate_xirr(guess)
        return self.xirr_result

    def __str__(self):
        """
        String representation of the computed XIRR.
        """
        return f"XIRR: {self.xirr_result:.2%}" if self.xirr_result is not None else "XIRR not yet computed."


## --- Financial Metrics --- ##
class FinancialMetrics:
    """
    A comprehensive class for analyzing drawdowns and calculating key performance metrics.
    """

    def __init__(self, nav_df, sip_amount=None, sip_dates=None, benchmark_nav=None, ter=None):
        """
        Initialize with NAV series and optionally SIP data and benchmark data.

        Parameters:
        nav_df (pd.DataFrame or pd.Series): NAV time series.
        sip_amount (float, optional): Monthly SIP amount.
        sip_dates (list or pd.DatetimeIndex, optional): Dates of SIPs.
        benchmark_nav (pd.Series, optional): Benchmark NAV time series aligned with nav_df.
        ter (float, optional): Total Expense Ratio as decimal (e.g., 0.01 for 1%).
        """
        self.nav_series = nav_df.squeeze().dropna()
        self.sip_amount = sip_amount
        self.sip_dates = pd.to_datetime(sip_dates) if sip_dates is not None else None
        self.benchmark_nav = benchmark_nav.squeeze().dropna() if benchmark_nav is not None else None
        self.ter = ter
        self.metrics = {}

        # Short circuit if not enough data
        if len(self.nav_series) < 252:
            self.metrics = {k: np.nan for k in [
                "CAGR", "MDD", "Avg_Top_5_DD", "Volatility", "Sharpe",
                "Sortino", "Rolling_1Y_Mean_Return", "Rolling_3Y_Mean_Return",
                "Rolling_3Y_Volatility", "Closing NAV",
                "Jensen Alpha", "Upside Capture Ratio", "Downside Capture Ratio"]}
            return

    # ====================== SIP-Based Metrics ======================

    def calculate_sip_metrics(self):
        """Calculate SIP metrics like total invested, return %, and CAGR."""
        if self.sip_amount is None or self.sip_dates is None:
            return
        
        total_invested = self.sip_amount * len(self.sip_dates)
        years = len(self.sip_dates) / 12

        self.metrics.update({
            "Total Invested": total_invested,
            "Investment Horizon": years,
        })

    def calculate_cagr(self):
        """Calculate CAGR."""
        start_nav = self.nav_series.iloc[0]
        end_nav = self.nav_series.iloc[-1]
        num_years = len(self.nav_series) / 252
        self.metrics["CAGR"] = (end_nav / start_nav) ** (1 / num_years) - 1 if num_years > 0 else np.nan

    def calculate_sharpe_ratio(self, risk_free_rate=0.007):
        returns = self.nav_series.pct_change().dropna()
        annual_return = returns.mean() * 252
        annual_volatility = returns.std() * np.sqrt(252)
        self.metrics["Sharpe"] = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else np.nan

    def calculate_sortino_ratio(self, risk_free_rate=0.007):
        returns = self.nav_series.pct_change().dropna()
        annual_return = returns.mean() * 252
        downside = returns[returns < 0]
        downside_deviation = downside.std() * np.sqrt(252)
        self.metrics["Sortino"] = (annual_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else np.nan

    def calculate_rolling_metrics(self, window_days=252):
        returns = self.nav_series.pct_change().dropna()
        rolling_windows = {
            "1Y": 1 * window_days,
            "3Y": 3 * window_days,
            "5Y": 5 * window_days,
            "7Y": 7 * window_days,
            "10Y": 10 * window_days
        }

        for label, window in rolling_windows.items():
            if len(returns) >= window:
                roll_r = self.nav_series.pct_change(window).dropna()
                self.metrics[f"Rolling_{label}_Mean_Return"] = roll_r.mean() / (window / 252)
                self.metrics[f"Rolling_{label}_Median_Return"] = roll_r.median() / (window / 252)

        self.metrics["Rolling_1Y_Volatility"] = returns.rolling(window_days).std().iloc[-1] * np.sqrt(252)

    def calculate_drawdowns(self):
        prices = self.nav_series
        drawdown = prices / prices.cummax() - 1
        starts, ends = [], []
        for i in range(1, len(drawdown)):
            if drawdown.iloc[i] == 0 and drawdown.iloc[i - 1] < 0:
                ends.append(i - 1)
            if drawdown.iloc[i] < 0 and drawdown.iloc[i - 1] == 0:
                starts.append(i)
        if len(starts) > len(ends):
            ends.append(len(drawdown) - 1)

        dd_info = []
        for s, e in zip(starts, ends):
            valley = drawdown[s:e + 1].idxmin()
            min_val = drawdown[s:e + 1].min()
            dd_info.append((s, valley, e, min_val))
        dd_df = pd.DataFrame(dd_info, columns=['Start', 'Valley', 'End', 'Drawdown'])

        self.metrics["MDD"] = dd_df['Drawdown'].min() if not dd_df.empty else np.nan
        self.metrics["Avg_Top_5_DD"] = dd_df.nsmallest(5, 'Drawdown')['Drawdown'].mean() if not dd_df.empty else np.nan
        self.metrics["Avg_Top_10_DD"] = dd_df.nsmallest(10, 'Drawdown')['Drawdown'].mean() if not dd_df.empty else np.nan

    def calculate_jensen_alpha(self, risk_free_rate=0.007):
        """
        Jensen Alpha = Portfolio return - [Risk-free rate + Beta * (Benchmark return - Risk-free rate)]
        Requires benchmark_nav.
        """
        if self.benchmark_nav is None:
            self.metrics["Jensen Alpha"] = np.nan
            return

        # Calculate daily returns
        port_returns = self.nav_series.pct_change().dropna()
        bench_returns = self.benchmark_nav.pct_change().dropna()

        # Align returns on index
        combined = pd.concat([port_returns, bench_returns], axis=1, join='inner')
        combined.columns = ['Portfolio', 'Benchmark']

        # Beta = Covariance(port, bench) / Variance(bench)
        cov = combined['Portfolio'].cov(combined['Benchmark'])
        var = combined['Benchmark'].var()
        beta = cov / var if var > 0 else np.nan

        # Annualized returns
        ann_port_return = combined['Portfolio'].mean() * 252
        ann_bench_return = combined['Benchmark'].mean() * 252

        # Jensen Alpha
        jensen_alpha = ann_port_return - (risk_free_rate + beta * (ann_bench_return - risk_free_rate))
        self.metrics["Jensen Alpha"] = jensen_alpha


    def calculate_capture_ratios(self):
        """
        Calculate Upside and Downside Capture Ratios.
        Requires benchmark_nav.
        """
        if self.benchmark_nav is None:
            self.metrics["Upside Capture Ratio"] = np.nan
            self.metrics["Downside Capture Ratio"] = np.nan
            return

        port_returns = self.nav_series.pct_change().dropna()
        bench_returns = self.benchmark_nav.pct_change().dropna()

        combined = pd.concat([port_returns, bench_returns], axis=1, join='inner')
        combined.columns = ['Portfolio', 'Benchmark']

        # Upside capture: avg portfolio return when benchmark > 0 divided by avg benchmark return > 0
        up_mask = combined['Benchmark'] > 0
        if up_mask.sum() > 0 and combined.loc[up_mask, 'Benchmark'].mean() != 0:
            upside_capture = combined.loc[up_mask, 'Portfolio'].mean() / combined.loc[up_mask, 'Benchmark'].mean()
        else:
            upside_capture = np.nan

        # Downside capture: avg portfolio return when benchmark < 0 divided by avg benchmark return < 0
        down_mask = combined['Benchmark'] < 0
        if down_mask.sum() > 0 and combined.loc[down_mask, 'Benchmark'].mean() != 0:
            downside_capture = combined.loc[down_mask, 'Portfolio'].mean() / combined.loc[down_mask, 'Benchmark'].mean()
        else:
            downside_capture = np.nan

        self.metrics["Upside Capture Ratio"] = upside_capture
        self.metrics["Downside Capture Ratio"] = downside_capture

    def generate_metrics(self):
        self.calculate_cagr()
        self.calculate_drawdowns()
        self.calculate_sharpe_ratio()
        self.calculate_sortino_ratio()
        self.calculate_rolling_metrics()
        self.calculate_sip_metrics()

        # New metrics
        self.calculate_jensen_alpha()
        self.calculate_capture_ratios()

        return self.metrics


## --- Non SIP Pseudo Nav --- ##
class NonSipPsuedoNav:
    def __init__(self, component_navs, portfolio_weights, initial_corpus=100):
        self.component_navs = component_navs
        self.portfolio_weights = portfolio_weights
        self.initial_corpus = initial_corpus
        self.portfolio_df = None

    def _validate_quarter_end_dates(self, dates, df):
        """Snap to last available NAV date <= each quarter-end date."""
        rebalance_dates_adjusted = []
        for date in dates:
            valid_dates = df.index[df.index <= date]
            if not valid_dates.empty:
                rebalance_dates_adjusted.append(valid_dates[-1])
        print("## List of Quarter-End dates generated. ##")
        return rebalance_dates_adjusted

    def build_portfolio(self, annual_tracking_error=0.015, management_fee=0.025, expense_ratio=0.01):
        # Fees deducted
        total_cost_deduction_fee = annual_tracking_error +  management_fee + expense_ratio

        # Step 1: Calculate initial units
        initial_units = {
            fund: (self.initial_corpus * weight) / self.component_navs.iloc[0][fund]
            for fund, weight in self.portfolio_weights.items()
        }

        # Step 2: Constant units across time
        pseudo_units_df = pd.DataFrame(initial_units, index=self.component_navs.index)

        # Step 3: Daily NAV of the portfolio
        portfolio_value = (pseudo_units_df * self.component_navs).sum(axis=1)
        portfolio_df = portfolio_value.to_frame(name='Non-SIP Portfolio Value')
        portfolio_df['dailyPctChange'] = portfolio_df['Non-SIP Portfolio Value'].pct_change()

        # Step 4: Apply quarterly tracking error
        quarter_end_dates = self.component_navs.resample('Q').last().index
        rebalance_dates = self._validate_quarter_end_dates(quarter_end_dates, self.component_navs)

        for date in rebalance_dates[:-1]:  # skip final date
            portfolio_df.loc[date, 'dailyPctChange'] -= total_cost_deduction_fee / 4

        # Step 5: Cumulative NAV adjustment
        portfolio_df['Non-SIP Portfolio Value (After Tracking Error)'] = (
            (1 + portfolio_df['dailyPctChange']).cumprod()
        )
        portfolio_df['Non-SIP Portfolio Value (After Tracking Error)'] = (
            portfolio_df['Non-SIP Portfolio Value (After Tracking Error)']
            / portfolio_df['Non-SIP Portfolio Value (After Tracking Error)'].dropna().iloc[0]
            * self.initial_corpus
        )

        self.portfolio_df = portfolio_df
        print("## Non-SIP Portfolio successfully built. ##")
        return self.portfolio_df

    def get_adjusted_nav_series(self):
        if self.portfolio_df is not None:
            return self.portfolio_df['Non-SIP Portfolio Value (After Tracking Error)']
        else:
            raise ValueError("Portfolio not built. Call build_portfolio() first.")
