"""
Excel Portfolio Analyzer - Comprehensive Example
===============================================

This example demonstrates the enhanced SIP Portfolio Analyzer with automatic
portfolio generation from Excel sheets (AUM/Vintage categories).

Features:
- Automatic portfolio generation from Excel with naming format 'sheetname_category_portfolio'
- Comprehensive analysis of all category-based portfolios
- Custom combination portfolios
- Complete comparison and export functionality

Author: Augment Agent
Date: 2025-06-29
"""

import pandas as pd
import numpy as np
from datetime import datetime
from sip_portfolio_analyzer import SIPPortfolioAnalyzer

def main_excel_analysis():
    """
    Main example using Excel file with AUM and Vintage sheets.
    """
    print("🚀 Excel Portfolio Analyzer - Comprehensive Example")
    print("=" * 70)
    
    # Step 1: Load NAV Data
    print("\n📊 Step 1: Loading NAV Data...")
    try:
        nav_data = pd.read_csv("input_data/nav_data.csv", index_col=0, parse_dates=True)
        print(f"✅ Loaded NAV data: {nav_data.shape[0]} rows, {nav_data.shape[1]} funds")
    except FileNotFoundError:
        print("❌ Error: nav_data.csv not found in input_data folder")
        return
    except Exception as e:
        print(f"❌ Error loading NAV data: {str(e)}")
        return
    
    # Step 2: Initialize Enhanced Analyzer
    print("\n🔧 Step 2: Initializing Enhanced SIP Portfolio Analyzer...")
    analyzer = SIPPortfolioAnalyzer(
        nav_data=nav_data,
        start_date="2015-01-01",  # Optional: filter start date
        end_date="2024-12-31"     # Optional: filter end date
    )
    
    # Step 3: Run Complete Excel Analysis Pipeline
    print("\n🎯 Step 3: Running Complete Excel Analysis Pipeline...")
    
    excel_file_path = r"MF_NAV\Scheme Names - Traditional SIP.xlsx"
    
    # Run the complete analysis pipeline
    results = analyzer.run_excel_portfolio_analysis(
        excel_file_path=excel_file_path,
        sip_amount=10000,                    # Monthly SIP amount
        sip_start_date="2018-01-01",         # SIP start date
        sip_end_date="2024-06-30",           # SIP end date
        benchmark_fund="Nifty 50",           # Benchmark for comparison
        min_funds_per_category=3,            # Minimum funds per category
        max_funds_per_category=8             # Maximum funds per category
    )
    
    if results.empty:
        print("❌ No results generated")
        return
    
    # Step 4: Display Detailed Results
    print("\n📈 Step 4: Detailed Results Analysis...")
    
    # Show portfolio breakdown by sheet and category
    print("\n📋 Portfolio Breakdown:")
    aum_portfolios = results[results['Portfolio Name'].str.contains('AUM_')]
    vintage_portfolios = results[results['Portfolio Name'].str.contains('Vintage_')]
    
    print(f"  - AUM-based portfolios: {len(aum_portfolios)}")
    print(f"  - Vintage-based portfolios: {len(vintage_portfolios)}")
    
    # Top performers by sheet
    if not aum_portfolios.empty:
        print(f"\n🏆 Top AUM Portfolio by XIRR:")
        top_aum = aum_portfolios.nlargest(1, 'XIRR (%)').iloc[0]
        print(f"  {top_aum['Portfolio Name']}: {top_aum['XIRR (%)']}% XIRR")
    
    if not vintage_portfolios.empty:
        print(f"\n🏆 Top Vintage Portfolio by XIRR:")
        top_vintage = vintage_portfolios.nlargest(1, 'XIRR (%)').iloc[0]
        print(f"  {top_vintage['Portfolio Name']}: {top_vintage['XIRR (%)']}% XIRR")
    
    # Step 5: Generate Comparison Plots
    print("\n📊 Step 5: Generating Comparison Plots...")
    
    # Plot XIRR comparison for all portfolios
    analyzer.plot_comparison(results, metric='XIRR (%)', top_n=min(15, len(results)), save_plot=True)
    
    # Plot CAGR comparison
    analyzer.plot_comparison(results, metric='CAGR (%)', top_n=min(15, len(results)), save_plot=True)
    
    # Plot Sharpe Ratio comparison
    analyzer.plot_comparison(results, metric='Sharpe Ratio', top_n=min(15, len(results)), save_plot=True)
    
    print("\n🎉 Excel Analysis Complete!")
    return results

def custom_combinations_example():
    """
    Example of creating custom portfolio combinations.
    """
    print("\n" + "="*70)
    print("🔄 Custom Portfolio Combinations Example")
    print("="*70)
    
    # Load data
    nav_data = pd.read_csv("input_data/nav_data.csv", index_col=0, parse_dates=True)
    analyzer = SIPPortfolioAnalyzer(nav_data)
    
    # Load Excel portfolios
    excel_file_path = r"MF_NAV\Scheme Names - Traditional SIP.xlsx"
    portfolios_dict = analyzer.load_excel_portfolios(excel_file_path)
    
    if not portfolios_dict:
        print("❌ No portfolios loaded")
        return
    
    # Create custom combinations
    combo_portfolios = analyzer.create_custom_combinations(portfolios_dict, combination_size=5)
    
    # Combine original and combination portfolios
    all_portfolios = {**portfolios_dict, **combo_portfolios}
    
    print(f"\n📊 Total portfolios for analysis: {len(all_portfolios)}")
    print(f"  - Original category portfolios: {len(portfolios_dict)}")
    print(f"  - Custom combination portfolios: {len(combo_portfolios)}")
    
    # Run analysis on all portfolios
    results = analyzer.analyze_portfolios(all_portfolios, sip_amount=15000)
    
    if not results.empty:
        # Export results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"custom_combinations_analysis_{timestamp}.xlsx"
        analyzer.export_comparison_table(results, filename)
        
        # Show top combination portfolios
        combo_results = results[results['Portfolio Name'].str.contains('Combo_')]
        if not combo_results.empty:
            print(f"\n🏆 Top 3 Combination Portfolios by XIRR:")
            top_combos = combo_results.nlargest(3, 'XIRR (%)')
            for idx, row in top_combos.iterrows():
                print(f"  {row['Portfolio Name']}: {row['XIRR (%)']}% XIRR")
    
    return results

def category_specific_analysis():
    """
    Example of analyzing specific categories only.
    """
    print("\n" + "="*70)
    print("🎯 Category-Specific Analysis Example")
    print("="*70)
    
    # Load data
    nav_data = pd.read_csv("input_data/nav_data.csv", index_col=0, parse_dates=True)
    analyzer = SIPPortfolioAnalyzer(nav_data)
    
    # Define specific categories of interest
    target_categories = ['Large_Cap_Fund', 'Mid_Cap_Fund', 'Small_cap_Fund', 'Multi_Cap_Fund']
    
    # Load Excel portfolios
    excel_file_path = r"MF_NAV\Scheme Names - Traditional SIP.xlsx"
    all_portfolios = analyzer.load_excel_portfolios(excel_file_path)
    
    # Filter for target categories
    filtered_portfolios = {}
    for portfolio_name, fund_list in all_portfolios.items():
        for target_cat in target_categories:
            if target_cat in portfolio_name:
                filtered_portfolios[portfolio_name] = fund_list
                break
    
    print(f"\n📊 Analyzing {len(filtered_portfolios)} portfolios from target categories:")
    for name in filtered_portfolios.keys():
        print(f"  - {name}")
    
    if filtered_portfolios:
        # Run focused analysis
        results = analyzer.analyze_portfolios(
            portfolios_dict=filtered_portfolios,
            sip_amount=20000,
            sip_start_date="2019-01-01",
            sip_end_date="2024-06-30"
        )
        
        if not results.empty:
            # Display results
            analyzer.display_top_performers(results, top_n=len(results))
            
            # Export focused results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"category_specific_analysis_{timestamp}.xlsx"
            analyzer.export_comparison_table(results, filename)
            
            print(f"\n📁 Category-specific analysis saved to: {filename}")
    
    return filtered_portfolios

def quick_excel_demo():
    """
    Quick demonstration of Excel integration.
    """
    print("\n" + "="*70)
    print("⚡ Quick Excel Integration Demo")
    print("="*70)
    
    try:
        # Load data
        nav_data = pd.read_csv("input_data/nav_data.csv", index_col=0, parse_dates=True)
        analyzer = SIPPortfolioAnalyzer(nav_data)
        
        # Quick Excel analysis
        excel_file_path = r"MF_NAV\Scheme Names - Traditional SIP.xlsx"
        
        # Just load portfolios to show structure
        portfolios_dict = analyzer.load_excel_portfolios(
            excel_file_path, 
            min_funds_per_category=3, 
            max_funds_per_category=5
        )
        
        print(f"\n📋 Generated Portfolio Structure:")
        for portfolio_name, fund_list in list(portfolios_dict.items())[:5]:  # Show first 5
            print(f"\n{portfolio_name}:")
            for fund in fund_list:
                print(f"  - {fund}")
        
        if len(portfolios_dict) > 5:
            print(f"\n... and {len(portfolios_dict) - 5} more portfolios")
        
        print(f"\n✅ Total portfolios generated: {len(portfolios_dict)}")
        
        return portfolios_dict
        
    except Exception as e:
        print(f"❌ Demo error: {str(e)}")
        return {}

if __name__ == "__main__":
    print("🎊 Excel Portfolio Analyzer - Multiple Examples")
    print("=" * 80)
    
    # Run main comprehensive example
    print("\n1️⃣ COMPREHENSIVE EXCEL ANALYSIS")
    main_results = main_excel_analysis()
    
    # Run custom combinations example
    print("\n2️⃣ CUSTOM COMBINATIONS EXAMPLE")
    combo_results = custom_combinations_example()
    
    # Run category-specific analysis
    print("\n3️⃣ CATEGORY-SPECIFIC ANALYSIS")
    category_portfolios = category_specific_analysis()
    
    # Run quick demo
    print("\n4️⃣ QUICK EXCEL INTEGRATION DEMO")
    demo_portfolios = quick_excel_demo()
    
    print("\n🎉 All Excel integration examples completed successfully!")
    print("=" * 80)
    print("📚 Key Features Demonstrated:")
    print("✅ Automatic portfolio generation from Excel sheets")
    print("✅ Portfolio naming: 'sheetname_category_portfolio'")
    print("✅ AUM and Vintage sheet processing")
    print("✅ Custom portfolio combinations")
    print("✅ Category-specific filtering")
    print("✅ Comprehensive analysis and export")
    print("✅ Equal-weighted SIP portfolios with XIRR calculation")
    print("=" * 80)
