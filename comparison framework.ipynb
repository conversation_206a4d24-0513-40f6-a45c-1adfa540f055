{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3c76414d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from dateutil.relativedelta import relativedelta\n", "import numpy as np\n", "import re\n", "import matplotlib.pyplot as plt\n", "from matplotlib import pyplot as plt\n", "from matplotlib.pyplot import figure\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "import math\n", "import os\n", "from datetime import date, timedelta, datetime\n", "import time\n", "from tqdm import tqdm\n", "import pyodbc\n", "import seaborn as sns\n", "from scipy import stats\n", "import xlsxwriter\n", "from matplotlib.ticker import MaxNLocator\n", "from matplotlib.backends.backend_pdf import PdfPages\n", "import itertools\n", "from data import Data\n", "d = Data()\n", "import numpy_financial as npf\n", "from scipy.optimize import newton, brentq\n", "start_time = time.perf_counter()"]}, {"cell_type": "code", "execution_count": null, "id": "14be0758", "metadata": {}, "outputs": [], "source": ["sheet1 = pd.read_excel('MF_NAV/Scheme Names - Traditional SIP.xlsx', sheet_name='Sheet1')"]}, {"cell_type": "code", "execution_count": null, "id": "7343c02d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>34</th>\n", "      <th>Bandhan Flexi Cap Fund-Reg(G)</th>\n", "      <th>Franklin India Equity Advantage Fund(G)</th>\n", "      <th>Franklin India Flexi Cap Fund(G)</th>\n", "      <th>HDFC Flexi Cap Fund(G)</th>\n", "      <th>HDFC Large and Mid Cap Fund-Reg(G)</th>\n", "      <th>ICICI Pru Large &amp; Mid Cap Fund(G)</th>\n", "      <th>Kotak Flexicap Fund(G)</th>\n", "      <th>Nippon India Vision Fund(G)</th>\n", "      <th>SBI Large &amp; Midcap Fund-Reg(G)</th>\n", "      <th>UTI Flexi Cap Fund-Reg(G)</th>\n", "      <th>...</th>\n", "      <th>Baroda BNP Paribas Multi Cap Fund-Reg(G)</th>\n", "      <th>DSP Small Cap Fund-Reg(G)</th>\n", "      <th>Franklin India Smaller Cos Fund(G)</th>\n", "      <th>HDFC Small Cap Fund-Reg(G)</th>\n", "      <th>ICICI Pru Multicap Fund(G)</th>\n", "      <th>Invesco India Multicap Fund(G)</th>\n", "      <th>Nippon India Multi Cap Fund(G)</th>\n", "      <th>SBI Small Cap Fund-Reg(G)</th>\n", "      <th>Sundaram Multi Cap Fund(G)</th>\n", "      <th>Sundaram Small Cap Fund(G)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2005-06-26</th>\n", "      <td>NaN</td>\n", "      <td>10.52</td>\n", "      <td>68.54</td>\n", "      <td>73.747</td>\n", "      <td>36.128751</td>\n", "      <td>47.11</td>\n", "      <td>NaN</td>\n", "      <td>91.96</td>\n", "      <td>35.795189</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>18.33</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>39.65</td>\n", "      <td>NaN</td>\n", "      <td>10.7006</td>\n", "      <td>NaN</td>\n", "      <td>28.18</td>\n", "      <td>11.3399</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2005-06-27</th>\n", "      <td>NaN</td>\n", "      <td>10.53</td>\n", "      <td>68.45</td>\n", "      <td>73.568</td>\n", "      <td>36.193382</td>\n", "      <td>47.13</td>\n", "      <td>NaN</td>\n", "      <td>92.33</td>\n", "      <td>35.574231</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>18.10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>39.60</td>\n", "      <td>NaN</td>\n", "      <td>10.7173</td>\n", "      <td>NaN</td>\n", "      <td>28.26</td>\n", "      <td>11.2684</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2005-06-28</th>\n", "      <td>NaN</td>\n", "      <td>10.40</td>\n", "      <td>67.76</td>\n", "      <td>72.409</td>\n", "      <td>35.728038</td>\n", "      <td>46.58</td>\n", "      <td>NaN</td>\n", "      <td>90.60</td>\n", "      <td>34.955548</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>17.67</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>38.98</td>\n", "      <td>NaN</td>\n", "      <td>10.5424</td>\n", "      <td>NaN</td>\n", "      <td>27.73</td>\n", "      <td>11.0649</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2005-06-29</th>\n", "      <td>NaN</td>\n", "      <td>10.50</td>\n", "      <td>68.48</td>\n", "      <td>72.955</td>\n", "      <td>36.064120</td>\n", "      <td>47.03</td>\n", "      <td>NaN</td>\n", "      <td>91.80</td>\n", "      <td>35.368003</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>17.95</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>39.37</td>\n", "      <td>NaN</td>\n", "      <td>10.6655</td>\n", "      <td>NaN</td>\n", "      <td>27.90</td>\n", "      <td>11.1449</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2005-06-30</th>\n", "      <td>NaN</td>\n", "      <td>10.57</td>\n", "      <td>69.07</td>\n", "      <td>73.768</td>\n", "      <td>36.348496</td>\n", "      <td>47.42</td>\n", "      <td>NaN</td>\n", "      <td>91.49</td>\n", "      <td>35.515308</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>17.96</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>39.61</td>\n", "      <td>NaN</td>\n", "      <td>10.6889</td>\n", "      <td>NaN</td>\n", "      <td>27.89</td>\n", "      <td>11.2323</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 30 columns</p>\n", "</div>"], "text/plain": ["34          Bandhan Flexi Cap Fund-Reg(G)  \\\n", "Date                                        \n", "2005-06-26                            NaN   \n", "2005-06-27                            NaN   \n", "2005-06-28                            NaN   \n", "2005-06-29                            NaN   \n", "2005-06-30                            NaN   \n", "\n", "34          Franklin India Equity Advantage Fund(G)  \\\n", "Date                                                  \n", "2005-06-26                                    10.52   \n", "2005-06-27                                    10.53   \n", "2005-06-28                                    10.40   \n", "2005-06-29                                    10.50   \n", "2005-06-30                                    10.57   \n", "\n", "34          Franklin India Flexi Cap Fund(G)  HDFC Flexi Cap Fund(G)  \\\n", "Date                                                                   \n", "2005-06-26                             68.54                  73.747   \n", "2005-06-27                             68.45                  73.568   \n", "2005-06-28                             67.76                  72.409   \n", "2005-06-29                             68.48                  72.955   \n", "2005-06-30                             69.07                  73.768   \n", "\n", "34          HDFC Large and Mid Cap Fund-Reg(G)  \\\n", "Date                                             \n", "2005-06-26                           36.128751   \n", "2005-06-27                           36.193382   \n", "2005-06-28                           35.728038   \n", "2005-06-29                           36.064120   \n", "2005-06-30                           36.348496   \n", "\n", "34          ICICI Pru Large & Mid Cap Fund(G)  Kotak Flexicap Fund(G)  \\\n", "Date                                                                    \n", "2005-06-26                              47.11                     NaN   \n", "2005-06-27                              47.13                     NaN   \n", "2005-06-28                              46.58                     NaN   \n", "2005-06-29                              47.03                     NaN   \n", "2005-06-30                              47.42                     NaN   \n", "\n", "34          Nippon India Vision Fund(G)  SBI Large & Midcap Fund-Reg(G)  \\\n", "Date                                                                      \n", "2005-06-26                        91.96                       35.795189   \n", "2005-06-27                        92.33                       35.574231   \n", "2005-06-28                        90.60                       34.955548   \n", "2005-06-29                        91.80                       35.368003   \n", "2005-06-30                        91.49                       35.515308   \n", "\n", "34          UTI Flexi Cap Fund-Reg(G)  ...  \\\n", "Date                                   ...   \n", "2005-06-26                        NaN  ...   \n", "2005-06-27                        NaN  ...   \n", "2005-06-28                        NaN  ...   \n", "2005-06-29                        NaN  ...   \n", "2005-06-30                        NaN  ...   \n", "\n", "34          Baroda BNP Paribas Multi Cap Fund-Reg(G)  \\\n", "Date                                                   \n", "2005-06-26                                     18.33   \n", "2005-06-27                                     18.10   \n", "2005-06-28                                     17.67   \n", "2005-06-29                                     17.95   \n", "2005-06-30                                     17.96   \n", "\n", "34          DSP Small Cap Fund-Reg(G)  Franklin India Smaller Cos Fund(G)  \\\n", "Date                                                                        \n", "2005-06-26                        NaN                                 NaN   \n", "2005-06-27                        NaN                                 NaN   \n", "2005-06-28                        NaN                                 NaN   \n", "2005-06-29                        NaN                                 NaN   \n", "2005-06-30                        NaN                                 NaN   \n", "\n", "34          HDFC Small Cap Fund-Reg(G)  ICICI Pru Multicap Fund(G)  \\\n", "Date                                                                 \n", "2005-06-26                         NaN                       39.65   \n", "2005-06-27                         NaN                       39.60   \n", "2005-06-28                         NaN                       38.98   \n", "2005-06-29                         NaN                       39.37   \n", "2005-06-30                         NaN                       39.61   \n", "\n", "34          Invesco India Multicap Fund(G)  Nippon India Multi Cap Fund(G)  \\\n", "Date                                                                         \n", "2005-06-26                             NaN                         10.7006   \n", "2005-06-27                             NaN                         10.7173   \n", "2005-06-28                             NaN                         10.5424   \n", "2005-06-29                             NaN                         10.6655   \n", "2005-06-30                             NaN                         10.6889   \n", "\n", "34          SBI Small Cap Fund-Reg(G)  Sundaram Multi Cap Fund(G)  \\\n", "Date                                                                \n", "2005-06-26                        NaN                       28.18   \n", "2005-06-27                        NaN                       28.26   \n", "2005-06-28                        NaN                       27.73   \n", "2005-06-29                        NaN                       27.90   \n", "2005-06-30                        NaN                       27.89   \n", "\n", "34          Sundaram Small Cap Fund(G)  \n", "Date                                    \n", "2005-06-26                     11.3399  \n", "2005-06-27                     11.2684  \n", "2005-06-28                     11.0649  \n", "2005-06-29                     11.1449  \n", "2005-06-30                     11.2323  \n", "\n", "[5 rows x 30 columns]"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["aum_sheet = pd.read_excel('MF_NAV/Scheme Names - Traditional SIP.xlsx', sheet_name='AUM')\n", "aum_sheet = aum_sheet.iloc[33:, 1:]\n", "aum_sheet.columns = aum_sheet.iloc[1]\n", "aum_sheet = aum_sheet.iloc[2:, :]\n", "aum_sheet = aum_sheet.dropna(how='all', axis=1)\n", "aum_sheet = aum_sheet.dropna(how='all', axis=0)\n", "aum_sheet.index = aum_sheet.iloc[:, 0]\n", "aum_sheet = aum_sheet.iloc[:, 1:]\n", "aum_sheet = aum_sheet.apply(pd.to_numeric, errors='coerce')\n", "aum_sheet.index.name = 'Date'\n", "aum_sheet.index = pd.to_datetime(aum_sheet.index) \n", "aum_sheet.head()\n"]}, {"cell_type": "code", "execution_count": 43, "id": "29bbae0d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>33</th>\n", "      <th>Aditya Birla SL Flexi Cap Fund(G)</th>\n", "      <th>Canara Rob Flexi Cap Fund-Reg(G)</th>\n", "      <th>Franklin India Flexi Cap Fund(G)</th>\n", "      <th>HDFC Flexi Cap Fund(G)</th>\n", "      <th>HDFC Large and Mid Cap Fund-Reg(G)</th>\n", "      <th>HSBC Flexi Cap Fund-Reg(G)</th>\n", "      <th>ICICI Pru Large &amp; Mid Cap Fund(G)</th>\n", "      <th>Nippon India Vision Fund(G)</th>\n", "      <th>SBI Large &amp; Midcap Fund-Reg(G)</th>\n", "      <th>Tata Large &amp; Mid Cap Fund-Reg(G)</th>\n", "      <th>...</th>\n", "      <th><PERSON><PERSON><PERSON> SL Small Cap Fund(G)</th>\n", "      <th>Baroda BNP Paribas Multi Cap Fund-Reg(G)</th>\n", "      <th>DSP Small Cap Fund-Reg(G)</th>\n", "      <th>Franklin India Smaller Cos Fund(G)</th>\n", "      <th>ICICI Pru Multicap Fund(G)</th>\n", "      <th>Invesco India Multicap Fund(G)</th>\n", "      <th>Kotak Small Cap Fund(G)</th>\n", "      <th>Nippon India Multi Cap Fund(G)</th>\n", "      <th>Sundaram Multi Cap Fund(G)</th>\n", "      <th>Sundaram Small Cap Fund(G)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2005-06-26</th>\n", "      <td>92.68</td>\n", "      <td>18.21</td>\n", "      <td>68.54</td>\n", "      <td>73.747</td>\n", "      <td>36.128751</td>\n", "      <td>13.8226</td>\n", "      <td>47.11</td>\n", "      <td>91.96</td>\n", "      <td>35.795189</td>\n", "      <td>30.8640</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>18.33</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>39.65</td>\n", "      <td>NaN</td>\n", "      <td>10.954</td>\n", "      <td>10.7006</td>\n", "      <td>28.18</td>\n", "      <td>11.3399</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2005-06-27</th>\n", "      <td>92.73</td>\n", "      <td>18.20</td>\n", "      <td>68.45</td>\n", "      <td>73.568</td>\n", "      <td>36.193382</td>\n", "      <td>13.7570</td>\n", "      <td>47.13</td>\n", "      <td>92.33</td>\n", "      <td>35.574231</td>\n", "      <td>30.7366</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>18.10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>39.60</td>\n", "      <td>NaN</td>\n", "      <td>10.919</td>\n", "      <td>10.7173</td>\n", "      <td>28.26</td>\n", "      <td>11.2684</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2005-06-28</th>\n", "      <td>91.16</td>\n", "      <td>17.81</td>\n", "      <td>67.76</td>\n", "      <td>72.409</td>\n", "      <td>35.728038</td>\n", "      <td>13.5115</td>\n", "      <td>46.58</td>\n", "      <td>90.60</td>\n", "      <td>34.955548</td>\n", "      <td>30.1704</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>17.67</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>38.98</td>\n", "      <td>NaN</td>\n", "      <td>10.812</td>\n", "      <td>10.5424</td>\n", "      <td>27.73</td>\n", "      <td>11.0649</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2005-06-29</th>\n", "      <td>92.26</td>\n", "      <td>17.92</td>\n", "      <td>68.48</td>\n", "      <td>72.955</td>\n", "      <td>36.064120</td>\n", "      <td>13.6402</td>\n", "      <td>47.03</td>\n", "      <td>91.80</td>\n", "      <td>35.368003</td>\n", "      <td>30.5055</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>17.95</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>39.37</td>\n", "      <td>NaN</td>\n", "      <td>10.849</td>\n", "      <td>10.6655</td>\n", "      <td>27.90</td>\n", "      <td>11.1449</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2005-06-30</th>\n", "      <td>92.72</td>\n", "      <td>17.95</td>\n", "      <td>69.07</td>\n", "      <td>73.768</td>\n", "      <td>36.348496</td>\n", "      <td>13.5603</td>\n", "      <td>47.42</td>\n", "      <td>91.49</td>\n", "      <td>35.515308</td>\n", "      <td>30.6377</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>17.96</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>39.61</td>\n", "      <td>NaN</td>\n", "      <td>10.954</td>\n", "      <td>10.6889</td>\n", "      <td>27.89</td>\n", "      <td>11.2323</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 30 columns</p>\n", "</div>"], "text/plain": ["33          Aditya Birla SL Flexi Cap Fund(G)  \\\n", "Date                                            \n", "2005-06-26                              92.68   \n", "2005-06-27                              92.73   \n", "2005-06-28                              91.16   \n", "2005-06-29                              92.26   \n", "2005-06-30                              92.72   \n", "\n", "33          Canara Rob Flexi Cap Fund-Reg(G)  \\\n", "Date                                           \n", "2005-06-26                             18.21   \n", "2005-06-27                             18.20   \n", "2005-06-28                             17.81   \n", "2005-06-29                             17.92   \n", "2005-06-30                             17.95   \n", "\n", "33          Franklin India Flexi Cap Fund(G)  HDFC Flexi Cap Fund(G)  \\\n", "Date                                                                   \n", "2005-06-26                             68.54                  73.747   \n", "2005-06-27                             68.45                  73.568   \n", "2005-06-28                             67.76                  72.409   \n", "2005-06-29                             68.48                  72.955   \n", "2005-06-30                             69.07                  73.768   \n", "\n", "33          HDFC Large and Mid Cap Fund-Reg(G)  HSBC Flexi Cap Fund-Reg(G)  \\\n", "Date                                                                         \n", "2005-06-26                           36.128751                     13.8226   \n", "2005-06-27                           36.193382                     13.7570   \n", "2005-06-28                           35.728038                     13.5115   \n", "2005-06-29                           36.064120                     13.6402   \n", "2005-06-30                           36.348496                     13.5603   \n", "\n", "33          ICICI Pru Large & Mid Cap Fund(G)  Nippon India Vision Fund(G)  \\\n", "Date                                                                         \n", "2005-06-26                              47.11                        91.96   \n", "2005-06-27                              47.13                        92.33   \n", "2005-06-28                              46.58                        90.60   \n", "2005-06-29                              47.03                        91.80   \n", "2005-06-30                              47.42                        91.49   \n", "\n", "33          SBI Large & Midcap Fund-Reg(G)  Tata Large & Mid Cap Fund-Reg(G)  \\\n", "Date                                                                           \n", "2005-06-26                       35.795189                           30.8640   \n", "2005-06-27                       35.574231                           30.7366   \n", "2005-06-28                       34.955548                           30.1704   \n", "2005-06-29                       35.368003                           30.5055   \n", "2005-06-30                       35.515308                           30.6377   \n", "\n", "33          ...  <PERSON><PERSON><PERSON> Birla SL Small Cap Fund(G)  \\\n", "Date        ...                                      \n", "2005-06-26  ...                                Na<PERSON>   \n", "2005-06-27  ...                                <PERSON><PERSON>   \n", "2005-06-28  ...                                Na<PERSON>   \n", "2005-06-29  ...                                Na<PERSON>   \n", "2005-06-30  ...                                Na<PERSON>   \n", "\n", "33          Baroda BNP Paribas Multi Cap Fund-Reg(G)  \\\n", "Date                                                   \n", "2005-06-26                                     18.33   \n", "2005-06-27                                     18.10   \n", "2005-06-28                                     17.67   \n", "2005-06-29                                     17.95   \n", "2005-06-30                                     17.96   \n", "\n", "33          DSP Small Cap Fund-Reg(G)  Franklin India Smaller Cos Fund(G)  \\\n", "Date                                                                        \n", "2005-06-26                        NaN                                 NaN   \n", "2005-06-27                        NaN                                 NaN   \n", "2005-06-28                        NaN                                 NaN   \n", "2005-06-29                        NaN                                 NaN   \n", "2005-06-30                        NaN                                 NaN   \n", "\n", "33          ICICI Pru Multicap Fund(G)  Invesco India Multicap Fund(G)  \\\n", "Date                                                                     \n", "2005-06-26                       39.65                             NaN   \n", "2005-06-27                       39.60                             NaN   \n", "2005-06-28                       38.98                             NaN   \n", "2005-06-29                       39.37                             NaN   \n", "2005-06-30                       39.61                             NaN   \n", "\n", "33          Kotak Small Cap Fund(G)  Nippon India Multi Cap Fund(G)  \\\n", "Date                                                                  \n", "2005-06-26                   10.954                         10.7006   \n", "2005-06-27                   10.919                         10.7173   \n", "2005-06-28                   10.812                         10.5424   \n", "2005-06-29                   10.849                         10.6655   \n", "2005-06-30                   10.954                         10.6889   \n", "\n", "33          Sundaram Multi Cap Fund(G)  Sundaram Small Cap Fund(G)  \n", "Date                                                                \n", "2005-06-26                       28.18                     11.3399  \n", "2005-06-27                       28.26                     11.2684  \n", "2005-06-28                       27.73                     11.0649  \n", "2005-06-29                       27.90                     11.1449  \n", "2005-06-30                       27.89                     11.2323  \n", "\n", "[5 rows x 30 columns]"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["vintage_sheet = pd.read_excel('MF_NAV/Scheme Names - Traditional SIP.xlsx', sheet_name='Vintage')\n", "vintage_sheet = vintage_sheet.iloc[32:, 1:]\n", "vintage_sheet.columns = vintage_sheet.iloc[1]\n", "vintage_sheet = vintage_sheet.iloc[2:, :]\n", "vintage_sheet = vintage_sheet.dropna(how='all', axis=1)\n", "vintage_sheet = vintage_sheet.dropna(how='all', axis=0)\n", "vintage_sheet.index = vintage_sheet.iloc[:, 0]\n", "vintage_sheet = vintage_sheet.iloc[:, 1:]\n", "vintage_sheet = vintage_sheet.apply(pd.to_numeric, errors='coerce')\n", "vintage_sheet.index.name = 'Date'\n", "vintage_sheet.index = pd.to_datetime(vintage_sheet.index)\n", "vintage_sheet.head()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}