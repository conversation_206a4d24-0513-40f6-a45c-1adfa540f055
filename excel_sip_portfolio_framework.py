"""
Excel SIP Portfolio Framework
============================

Complete framework that loads NAV data directly from the Excel file and creates
portfolio combinations from AUM and Vintage sheets with automatic category detection.
Portfolio naming format: 'sheetname_category_portfolio'

Author: Augment Agent
Date: 2025-06-29
"""

import pandas as pd
import numpy as np
from datetime import datetime
from sip_portfolio_analyzer import SIPPortfolioAnalyzer
from collections import defaultdict

def load_nav_data_from_excel(excel_file_path: str) -> pd.DataFrame:
    """
    Load NAV data from Excel file using the exact method from the notebook.
    """
    print("📊 Loading NAV data from Excel file...")
    
    # Load AUM sheet
    aum_sheet = pd.read_excel(excel_file_path, sheet_name='AUM')
    aum_sheet = aum_sheet.iloc[33:, 1:]
    aum_sheet.columns = aum_sheet.iloc[1]
    aum_sheet = aum_sheet.iloc[2:, :]
    aum_sheet = aum_sheet.dropna(how='all', axis=1)
    aum_sheet = aum_sheet.dropna(how='all', axis=0)
    aum_sheet.index = aum_sheet.iloc[:, 0]
    aum_sheet = aum_sheet.iloc[:, 1:]
    aum_sheet = aum_sheet.apply(pd.to_numeric, errors='coerce')
    aum_sheet.index.name = 'Date'
    aum_sheet.index = pd.to_datetime(aum_sheet.index)
    
    # Load Vintage sheet
    vintage_sheet = pd.read_excel(excel_file_path, sheet_name='Vintage')
    vintage_sheet = vintage_sheet.iloc[32:, 1:]
    vintage_sheet.columns = vintage_sheet.iloc[1]
    vintage_sheet = vintage_sheet.iloc[2:, :]
    vintage_sheet = vintage_sheet.dropna(how='all', axis=1)
    vintage_sheet = vintage_sheet.dropna(how='all', axis=0)
    vintage_sheet.index = vintage_sheet.iloc[:, 0]
    vintage_sheet = vintage_sheet.iloc[:, 1:]
    vintage_sheet = vintage_sheet.apply(pd.to_numeric, errors='coerce')
    vintage_sheet.index.name = 'Date'
    vintage_sheet.index = pd.to_datetime(vintage_sheet.index)
    
    # Merge the datasets
    df = aum_sheet.merge(vintage_sheet, left_index=True, right_index=True, how='outer', suffixes=('', '_drop')).ffill()
    # Drop duplicate columns
    df = df.loc[:, ~df.columns.str.endswith('_drop')]
    
    print(f"✅ Loaded NAV data: {df.shape}")
    print(f"📅 Date range: {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}")
    print(f"💼 Available funds: {len(df.columns)}")
    
    return df

def get_fund_categories_from_excel(excel_file_path: str) -> tuple:
    """
    Extract fund categories from the Excel file's AUM and Vintage sheets.
    """
    print("📋 Extracting fund categories from Excel file...")

    try:
        # Read AUM sheet metadata (fund names and categories)
        print("  📊 Reading AUM sheet metadata...")
        aum_meta = pd.read_excel(excel_file_path, sheet_name='AUM')
        print(f"  Raw AUM shape: {aum_meta.shape}")
        print(f"  AUM columns: {aum_meta.columns.tolist()[:5]}")

        # Find the header row for AUM (look for 'Scheme Name')
        header_row = None
        for i in range(min(10, len(aum_meta))):
            if 'Scheme Name' in str(aum_meta.iloc[i].values):
                header_row = i
                break

        if header_row is not None:
            print(f"  Found AUM header at row {header_row}")
            aum_meta = pd.read_excel(excel_file_path, sheet_name='AUM', header=header_row)
            aum_meta = aum_meta.dropna(how='all')
            aum_meta = aum_meta[aum_meta['Scheme Name'].notna()]
            aum_meta = aum_meta[aum_meta['Scheme Name'] != 'Scheme Name']  # Remove header duplicates
        else:
            print("  ⚠️ Could not find AUM header row")
            aum_meta = pd.DataFrame()

        # Read Vintage sheet metadata
        print("  📈 Reading Vintage sheet metadata...")
        vintage_meta = pd.read_excel(excel_file_path, sheet_name='Vintage')
        print(f"  Raw Vintage shape: {vintage_meta.shape}")
        print(f"  Vintage columns: {vintage_meta.columns.tolist()[:5]}")

        # Find the header row for Vintage
        header_row = None
        for i in range(min(10, len(vintage_meta))):
            if 'Scheme Name' in str(vintage_meta.iloc[i].values):
                header_row = i
                break

        if header_row is not None:
            print(f"  Found Vintage header at row {header_row}")
            vintage_meta = pd.read_excel(excel_file_path, sheet_name='Vintage', header=header_row)
            vintage_meta = vintage_meta.dropna(how='all')
            vintage_meta = vintage_meta[vintage_meta['Scheme Name'].notna()]
            vintage_meta = vintage_meta[vintage_meta['Scheme Name'] != 'Scheme Name']  # Remove header duplicates
        else:
            print("  ⚠️ Could not find Vintage header row")
            vintage_meta = pd.DataFrame()

        print(f"✅ AUM metadata: {len(aum_meta)} funds")
        print(f"✅ Vintage metadata: {len(vintage_meta)} funds")

        if not aum_meta.empty:
            print(f"  AUM categories: {aum_meta['Category'].unique()[:5]}")
        if not vintage_meta.empty:
            print(f"  Vintage categories: {vintage_meta['Category'].unique()[:5]}")

        return aum_meta, vintage_meta

    except Exception as e:
        print(f"❌ Error extracting categories: {str(e)}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame(), pd.DataFrame()

def create_category_portfolios(nav_data: pd.DataFrame, aum_meta: pd.DataFrame, vintage_meta: pd.DataFrame) -> dict:
    """
    Create portfolio combinations based on categories with naming format 'sheetname_category_portfolio'.
    """
    print("🎯 Creating category-based portfolios...")
    
    portfolios_dict = {}
    available_funds = set(nav_data.columns)
    
    # Process AUM sheet categories
    print("\n📊 Processing AUM categories:")
    aum_categories = aum_meta.groupby('Category')['Scheme Name'].apply(list).to_dict()
    
    for category, funds in aum_categories.items():
        if pd.notna(category) and category != '':
            # Filter funds that are available in NAV data
            available_category_funds = [fund for fund in funds if fund in available_funds]
            
            if len(available_category_funds) >= 3:  # Minimum 3 funds per portfolio
                portfolio_name = f"AUM_{category.replace(' ', '_').replace('&', 'and').replace('/', '_')}_portfolio"
                portfolios_dict[portfolio_name] = available_category_funds
                print(f"  ✅ {portfolio_name}: {len(available_category_funds)} funds")
            else:
                print(f"  ⚠️ {category}: Only {len(available_category_funds)} available funds (minimum 3 required)")
    
    # Process Vintage sheet categories
    print("\n📈 Processing Vintage categories:")
    vintage_categories = vintage_meta.groupby('Category')['Scheme Name'].apply(list).to_dict()
    
    for category, funds in vintage_categories.items():
        if pd.notna(category) and category != '':
            # Filter funds that are available in NAV data
            available_category_funds = [fund for fund in funds if fund in available_funds]
            
            if len(available_category_funds) >= 3:  # Minimum 3 funds per portfolio
                portfolio_name = f"Vintage_{category.replace(' ', '_').replace('&', 'and').replace('/', '_')}_portfolio"
                portfolios_dict[portfolio_name] = available_category_funds
                print(f"  ✅ {portfolio_name}: {len(available_category_funds)} funds")
            else:
                print(f"  ⚠️ {category}: Only {len(available_category_funds)} available funds (minimum 3 required)")
    
    print(f"\n🎉 Created {len(portfolios_dict)} portfolios total")
    return portfolios_dict

def run_complete_excel_analysis(excel_file_path: str = 'MF_NAV/Scheme Names - Traditional SIP.xlsx',
                               sip_amount: float = 10000,
                               sip_start_date: str = "2018-01-01",
                               sip_end_date: str = "2024-06-30",
                               benchmark_fund: str = None) -> pd.DataFrame:
    """
    Complete analysis pipeline using Excel file data.
    """
    print("🚀 Complete Excel SIP Portfolio Analysis")
    print("=" * 70)
    
    # Step 1: Load NAV data from Excel
    try:
        nav_data = load_nav_data_from_excel(excel_file_path)
    except Exception as e:
        print(f"❌ Error loading NAV data: {str(e)}")
        return pd.DataFrame()
    
    # Step 2: Extract fund categories
    try:
        aum_meta, vintage_meta = get_fund_categories_from_excel(excel_file_path)
        if aum_meta.empty and vintage_meta.empty:
            print("❌ No category metadata found")
            return pd.DataFrame()
    except Exception as e:
        print(f"❌ Error extracting categories: {str(e)}")
        return pd.DataFrame()
    
    # Step 3: Create category-based portfolios
    try:
        portfolios_dict = create_category_portfolios(nav_data, aum_meta, vintage_meta)
        if not portfolios_dict:
            print("❌ No portfolios created")
            return pd.DataFrame()
    except Exception as e:
        print(f"❌ Error creating portfolios: {str(e)}")
        return pd.DataFrame()
    
    # Step 4: Initialize analyzer
    print(f"\n🔧 Initializing SIP Portfolio Analyzer...")
    try:
        analyzer = SIPPortfolioAnalyzer(nav_data)
        print("✅ Analyzer initialized successfully")
    except Exception as e:
        print(f"❌ Error initializing analyzer: {str(e)}")
        return pd.DataFrame()
    
    # Step 5: Run comprehensive analysis
    print(f"\n📊 Running comprehensive analysis...")
    print(f"💰 SIP Amount: ₹{sip_amount:,} per month")
    print(f"📅 SIP Period: {sip_start_date} to {sip_end_date}")
    if benchmark_fund:
        print(f"📊 Benchmark: {benchmark_fund}")
    
    try:
        results = analyzer.analyze_portfolios(
            portfolios_dict=portfolios_dict,
            sip_amount=sip_amount,
            sip_start_date=sip_start_date,
            sip_end_date=sip_end_date,
            benchmark_fund=benchmark_fund
        )
        
        if results.empty:
            print("❌ Analysis failed - no results generated")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"❌ Error in analysis: {str(e)}")
        return pd.DataFrame()
    
    # Step 6: Display results
    print(f"\n📈 Analysis Results:")
    print(f"✅ Successfully analyzed {len(results)} portfolios")
    
    # Show key results
    key_columns = ['Portfolio Name', 'Number of Funds', 'XIRR (%)', 'CAGR (%)', 'Sharpe Ratio', 'MDD (%)']
    print(f"\n📊 Key Results:")
    print(results[key_columns].to_string(index=False))
    
    # Display top performers
    analyzer.display_top_performers(results, top_n=5)
    
    # Step 7: Category comparison
    print(f"\n🏆 AUM vs Vintage Comparison:")
    aum_results = results[results['Portfolio Name'].str.contains('AUM_')]
    vintage_results = results[results['Portfolio Name'].str.contains('Vintage_')]
    
    if not aum_results.empty and not vintage_results.empty:
        aum_avg_xirr = aum_results['XIRR (%)'].mean()
        vintage_avg_xirr = vintage_results['XIRR (%)'].mean()
        
        print(f"  📊 Average AUM Portfolio XIRR: {aum_avg_xirr:.2f}%")
        print(f"  📈 Average Vintage Portfolio XIRR: {vintage_avg_xirr:.2f}%")
        print(f"  🥇 Better Strategy: {'AUM' if aum_avg_xirr > vintage_avg_xirr else 'Vintage'}")
    
    # Step 8: Export results
    print(f"\n💾 Exporting results...")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_filename = f"excel_sip_portfolio_analysis_{timestamp}.xlsx"
    analyzer.export_comparison_table(results, excel_filename)
    
    # Step 9: Generate plots
    print(f"\n📊 Generating comparison plots...")
    analyzer.plot_comparison(results, metric='XIRR (%)', top_n=min(15, len(results)), save_plot=True)
    analyzer.plot_comparison(results, metric='CAGR (%)', top_n=min(15, len(results)), save_plot=True)
    analyzer.plot_comparison(results, metric='Sharpe Ratio', top_n=min(15, len(results)), save_plot=True)
    
    print(f"\n🎉 Complete Excel Analysis Finished!")
    print(f"📁 Results exported to: {excel_filename}")
    print(f"📊 Comparison plots saved as PNG files")
    
    return results

if __name__ == "__main__":
    print("🎊 Excel SIP Portfolio Framework")
    print("=" * 70)
    
    # Run the complete analysis
    results = run_complete_excel_analysis()
    
    if not results.empty:
        print("\n" + "=" * 70)
        print("📚 Analysis Summary:")
        print("✅ Loaded NAV data directly from Excel file")
        print("✅ Extracted fund categories from AUM and Vintage sheets")
        print("✅ Created portfolios with naming format: 'sheetname_category_portfolio'")
        print("✅ Analyzed all portfolio combinations")
        print("✅ Generated comprehensive comparison table")
        print("✅ Calculated XIRR for equal-weighted SIP portfolios")
        print("✅ Calculated all non-SIP performance metrics")
        print("✅ Exported results and generated plots")
        print("=" * 70)
    else:
        print("\n❌ Analysis could not be completed")
        print("🔧 Please check the Excel file path and structure")
