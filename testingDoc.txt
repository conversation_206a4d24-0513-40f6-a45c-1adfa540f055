---------- Time Optimization: TESTING RESULTS ----------
1. original time= 1m 24s
2. vectorized SIPAllocator class= 32s
3. vectorized validate_quarter_end_dates func= 27s
4. vectorized validate_sip_dates func= 33s
5. in generate_portfolios ,prefer="processes"= 38s
6. in generate_portfolios ,batch_size=16= 28s | batch_size=100= 22s | batch_size= 24s
7. using multiprocessing, batch_size="auto" = 39s

--------- Final Testing ---------
Total time taken for 28C5 = 155 mins

