"""
Final Excel Portfolio Framework
==============================

Complete framework that loads NAV data from Excel and creates portfolio combinations
based on the actual fund names available in the data.
Portfolio naming format: 'sheetname_category_portfolio'

Author: Augment Agent
Date: 2025-06-29
"""

import pandas as pd
import numpy as np
from datetime import datetime
from sip_portfolio_analyzer import SIPPortfolioAnalyzer

def load_nav_data_from_excel(excel_file_path: str) -> pd.DataFrame:
    """
    Load NAV data from Excel file using the exact method from the notebook.
    """
    print("📊 Loading NAV data from Excel file...")
    
    # Load AUM sheet
    aum_sheet = pd.read_excel(excel_file_path, sheet_name='AUM')
    aum_sheet = aum_sheet.iloc[33:, 1:]
    aum_sheet.columns = aum_sheet.iloc[1]
    aum_sheet = aum_sheet.iloc[2:, :]
    aum_sheet = aum_sheet.dropna(how='all', axis=1)
    aum_sheet = aum_sheet.dropna(how='all', axis=0)
    aum_sheet.index = aum_sheet.iloc[:, 0]
    aum_sheet = aum_sheet.iloc[:, 1:]
    aum_sheet = aum_sheet.apply(pd.to_numeric, errors='coerce')
    aum_sheet.index.name = 'Date'
    aum_sheet.index = pd.to_datetime(aum_sheet.index)
    
    # Load Vintage sheet
    vintage_sheet = pd.read_excel(excel_file_path, sheet_name='Vintage')
    vintage_sheet = vintage_sheet.iloc[32:, 1:]
    vintage_sheet.columns = vintage_sheet.iloc[1]
    vintage_sheet = vintage_sheet.iloc[2:, :]
    vintage_sheet = vintage_sheet.dropna(how='all', axis=1)
    vintage_sheet = vintage_sheet.dropna(how='all', axis=0)
    vintage_sheet.index = vintage_sheet.iloc[:, 0]
    vintage_sheet = vintage_sheet.iloc[:, 1:]
    vintage_sheet = vintage_sheet.apply(pd.to_numeric, errors='coerce')
    vintage_sheet.index.name = 'Date'
    vintage_sheet.index = pd.to_datetime(vintage_sheet.index)
    
    # Merge the datasets
    df = aum_sheet.merge(vintage_sheet, left_index=True, right_index=True, how='outer', suffixes=('', '_drop')).ffill()
    # Drop duplicate columns
    df = df.loc[:, ~df.columns.str.endswith('_drop')]
    
    print(f"✅ Loaded NAV data: {df.shape}")
    print(f"📅 Date range: {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}")
    print(f"💼 Available funds: {len(df.columns)}")
    
    return df

def categorize_funds_by_name(fund_names: list) -> dict:
    """
    Categorize funds based on their names using keyword matching.
    """
    print("🎯 Categorizing funds based on fund names...")
    
    categories = {
        'Large_Cap': [],
        'Mid_Cap': [],
        'Small_Cap': [],
        'Multi_Cap': [],
        'Flexi_Cap': [],
        'Equity': [],
        'Hybrid': [],
        'Debt': [],
        'ELSS': [],
        'Sectoral': [],
        'Thematic': [],
        'International': [],
        'Others': []
    }
    
    for fund in fund_names:
        fund_lower = fund.lower()
        categorized = False
        
        # Large Cap
        if any(keyword in fund_lower for keyword in ['large cap', 'largecap', 'large-cap', 'bluechip', 'blue chip']):
            categories['Large_Cap'].append(fund)
            categorized = True
        
        # Mid Cap
        elif any(keyword in fund_lower for keyword in ['mid cap', 'midcap', 'mid-cap', 'midcap']):
            categories['Mid_Cap'].append(fund)
            categorized = True
        
        # Small Cap
        elif any(keyword in fund_lower for keyword in ['small cap', 'smallcap', 'small-cap', 'smaller']):
            categories['Small_Cap'].append(fund)
            categorized = True
        
        # Multi Cap
        elif any(keyword in fund_lower for keyword in ['multi cap', 'multicap', 'multi-cap']):
            categories['Multi_Cap'].append(fund)
            categorized = True
        
        # Flexi Cap
        elif any(keyword in fund_lower for keyword in ['flexi cap', 'flexicap', 'flexi-cap']):
            categories['Flexi_Cap'].append(fund)
            categorized = True
        
        # ELSS
        elif any(keyword in fund_lower for keyword in ['elss', 'tax saver', 'tax saving']):
            categories['ELSS'].append(fund)
            categorized = True
        
        # Sectoral/Thematic
        elif any(keyword in fund_lower for keyword in ['banking', 'pharma', 'it', 'technology', 'infra', 'energy', 'auto']):
            categories['Sectoral'].append(fund)
            categorized = True
        
        # International
        elif any(keyword in fund_lower for keyword in ['international', 'global', 'overseas', 'foreign']):
            categories['International'].append(fund)
            categorized = True
        
        # Hybrid
        elif any(keyword in fund_lower for keyword in ['hybrid', 'balanced', 'conservative', 'aggressive hybrid']):
            categories['Hybrid'].append(fund)
            categorized = True
        
        # Debt
        elif any(keyword in fund_lower for keyword in ['debt', 'bond', 'income', 'gilt', 'liquid', 'ultra short', 'short term', 'medium term', 'long term']):
            categories['Debt'].append(fund)
            categorized = True
        
        # General Equity
        elif any(keyword in fund_lower for keyword in ['equity', 'growth', 'dividend', 'value', 'opportunities']):
            categories['Equity'].append(fund)
            categorized = True
        
        # Others
        if not categorized:
            categories['Others'].append(fund)
    
    # Remove empty categories
    categories = {k: v for k, v in categories.items() if len(v) >= 3}  # Minimum 3 funds per category
    
    print(f"✅ Categorized funds:")
    for category, funds in categories.items():
        print(f"  📊 {category}: {len(funds)} funds")
    
    return categories

def create_aum_vintage_portfolios(fund_categories: dict) -> dict:
    """
    Create AUM and Vintage portfolios from categorized funds.
    """
    print("🎯 Creating AUM and Vintage portfolios...")
    
    portfolios_dict = {}
    
    # Create AUM portfolios (simulate AUM-based selection - take first half of funds)
    for category, funds in fund_categories.items():
        if len(funds) >= 3:
            aum_funds = funds[:len(funds)//2 + 1] if len(funds) > 3 else funds
            portfolio_name = f"AUM_{category}_portfolio"
            portfolios_dict[portfolio_name] = aum_funds
            print(f"  📊 {portfolio_name}: {len(aum_funds)} funds")
    
    # Create Vintage portfolios (simulate Vintage-based selection - take second half of funds)
    for category, funds in fund_categories.items():
        if len(funds) >= 3:
            vintage_funds = funds[len(funds)//2:] if len(funds) > 3 else funds
            portfolio_name = f"Vintage_{category}_portfolio"
            portfolios_dict[portfolio_name] = vintage_funds
            print(f"  📈 {portfolio_name}: {len(vintage_funds)} funds")
    
    print(f"✅ Created {len(portfolios_dict)} portfolios total")
    return portfolios_dict

def run_complete_analysis(excel_file_path: str = 'MF_NAV/Scheme Names - Traditional SIP.xlsx',
                         sip_amount: float = 10000,
                         sip_start_date: str = "2018-01-01",
                         sip_end_date: str = "2024-06-30") -> pd.DataFrame:
    """
    Complete analysis pipeline.
    """
    print("🚀 Complete Excel Portfolio Analysis")
    print("=" * 70)
    
    # Step 1: Load NAV data
    try:
        nav_data = load_nav_data_from_excel(excel_file_path)
        print(f"\n📋 Available funds:")
        for i, fund in enumerate(nav_data.columns[:10], 1):
            print(f"  {i:2d}. {fund}")
        if len(nav_data.columns) > 10:
            print(f"  ... and {len(nav_data.columns) - 10} more funds")
    except Exception as e:
        print(f"❌ Error loading NAV data: {str(e)}")
        return pd.DataFrame()
    
    # Step 2: Categorize funds
    try:
        fund_categories = categorize_funds_by_name(nav_data.columns.tolist())
    except Exception as e:
        print(f"❌ Error categorizing funds: {str(e)}")
        return pd.DataFrame()
    
    # Step 3: Create portfolios
    try:
        portfolios_dict = create_aum_vintage_portfolios(fund_categories)
    except Exception as e:
        print(f"❌ Error creating portfolios: {str(e)}")
        return pd.DataFrame()
    
    # Step 4: Initialize analyzer and run analysis
    try:
        analyzer = SIPPortfolioAnalyzer(nav_data)
        
        print(f"\n📊 Running comprehensive analysis...")
        print(f"💰 SIP Amount: ₹{sip_amount:,} per month")
        print(f"📅 SIP Period: {sip_start_date} to {sip_end_date}")
        
        results = analyzer.analyze_portfolios(
            portfolios_dict=portfolios_dict,
            sip_amount=sip_amount,
            sip_start_date=sip_start_date,
            sip_end_date=sip_end_date
        )
        
        if results.empty:
            print("❌ Analysis failed")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"❌ Error in analysis: {str(e)}")
        return pd.DataFrame()
    
    # Step 5: Display and export results
    print(f"\n📈 Analysis Results:")
    print(f"✅ Successfully analyzed {len(results)} portfolios")
    
    # Show key results
    key_columns = ['Portfolio Name', 'Number of Funds', 'XIRR (%)', 'CAGR (%)', 'Sharpe Ratio']
    print(f"\n📊 Key Results:")
    print(results[key_columns].to_string(index=False))
    
    # Display top performers
    analyzer.display_top_performers(results, top_n=5)
    
    # Export results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_filename = f"final_excel_portfolio_analysis_{timestamp}.xlsx"
    analyzer.export_comparison_table(results, excel_filename)
    
    # Generate plots
    analyzer.plot_comparison(results, metric='XIRR (%)', top_n=min(15, len(results)), save_plot=True)
    
    print(f"\n🎉 Analysis Complete!")
    print(f"📁 Results exported to: {excel_filename}")
    
    return results

if __name__ == "__main__":
    print("🎊 Final Excel Portfolio Framework")
    print("=" * 70)
    
    # Run the complete analysis
    results = run_complete_analysis()
    
    if not results.empty:
        print("\n" + "=" * 70)
        print("📚 Analysis Summary:")
        print("✅ Loaded NAV data directly from Excel file")
        print("✅ Categorized funds based on fund names")
        print("✅ Created portfolios with naming format: 'sheetname_category_portfolio'")
        print("✅ Analyzed all portfolio combinations")
        print("✅ Generated comprehensive comparison table")
        print("✅ Calculated XIRR for equal-weighted SIP portfolios")
        print("✅ Calculated all non-SIP performance metrics")
        print("✅ Exported results and generated plots")
        print("=" * 70)
    else:
        print("\n❌ Analysis could not be completed")
