"""
Test Script for SIP Portfolio Analyzer
=====================================

Quick test to verify the streamlined SIP Portfolio Analyzer works correctly.

Author: Augment Agent
Date: 2025-06-29
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

def test_sip_analyzer():
    """
    Test the SIP Portfolio Analyzer with a simple example.
    """
    print("🧪 Testing SIP Portfolio Analyzer")
    print("=" * 50)
    
    try:
        # Import the analyzer
        from sip_portfolio_analyzer import SIPPortfolioAnalyzer
        print("✅ SIP Portfolio Analyzer imported successfully")
    except ImportError as e:
        print(f"❌ Error importing analyzer: {e}")
        return False
    
    try:
        # Load NAV data
        nav_data = pd.read_csv("input_data/nav_data.csv", index_col=0, parse_dates=True)
        print(f"✅ NAV data loaded: {nav_data.shape[0]} rows, {nav_data.shape[1]} funds")
    except FileNotFoundError:
        print("❌ Error: nav_data.csv not found in input_data folder")
        return False
    except Exception as e:
        print(f"❌ Error loading NAV data: {e}")
        return False
    
    try:
        # Initialize analyzer
        analyzer = SIPPortfolioAnalyzer(
            nav_data=nav_data,
            start_date="2020-01-01",
            end_date="2024-06-30"
        )
        print("✅ Analyzer initialized successfully")
    except Exception as e:
        print(f"❌ Error initializing analyzer: {e}")
        return False
    
    try:
        # Get available funds
        available_funds = analyzer.get_available_funds()
        print(f"✅ Available funds: {len(available_funds)}")
        print(f"📋 Sample funds: {available_funds[:3]}")
    except Exception as e:
        print(f"❌ Error getting available funds: {e}")
        return False
    
    try:
        # Create test portfolio dictionary
        test_portfolios = {
            'Test Portfolio A': available_funds[:3],  # First 3 funds
            'Test Portfolio B': available_funds[3:6], # Next 3 funds
            'Test Portfolio C': available_funds[:5]   # First 5 funds
        }
        
        print(f"✅ Test portfolios created:")
        for name, funds in test_portfolios.items():
            print(f"  - {name}: {len(funds)} funds")
            
    except Exception as e:
        print(f"❌ Error creating test portfolios: {e}")
        return False
    
    try:
        # Validate portfolios
        valid_portfolios, errors = analyzer.validate_portfolios_dict(test_portfolios)
        
        if errors:
            print("⚠️ Validation errors:")
            for error in errors:
                print(f"  - {error}")
        
        print(f"✅ Portfolio validation: {len(valid_portfolios)}/{len(test_portfolios)} valid")
        
    except Exception as e:
        print(f"❌ Error validating portfolios: {e}")
        return False
    
    try:
        # Run analysis
        print("\n🔍 Running test analysis...")
        
        comparison_table = analyzer.analyze_portfolios(
            portfolios_dict=valid_portfolios,
            sip_amount=5000,
            sip_start_date="2021-01-01",
            sip_end_date="2024-06-30"
        )
        
        print("✅ Analysis completed successfully")
        
        # Check results
        if not comparison_table.empty:
            print(f"📊 Results Summary:")
            print(f"  - Portfolios analyzed: {len(comparison_table)}")
            
            # Display key metrics for first portfolio
            first_row = comparison_table.iloc[0]
            print(f"  - Sample results for '{first_row['Portfolio Name']}':")
            print(f"    * XIRR: {first_row['XIRR (%)']}%")
            print(f"    * CAGR: {first_row['CAGR (%)']}%")
            print(f"    * Sharpe: {first_row['Sharpe Ratio']}")
            print(f"    * MDD: {first_row['MDD (%)']}%")
            
            # Test display function
            analyzer.display_top_performers(comparison_table, top_n=2)
            
        else:
            print("⚠️ No results generated")
            
    except Exception as e:
        print(f"❌ Error running analysis: {e}")
        return False
    
    try:
        # Test export functionality
        test_filename = f"test_sip_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        exported_file = analyzer.export_comparison_table(comparison_table, test_filename)
        
        if exported_file and os.path.exists(exported_file):
            print(f"✅ Results exported successfully: {exported_file}")
            # Clean up test file
            os.remove(exported_file)
            print("🧹 Test file cleaned up")
        else:
            print("⚠️ Export test failed")
            
    except Exception as e:
        print(f"❌ Error testing export: {e}")
        return False
    
    print("\n🎉 All tests passed successfully!")
    print("✅ SIP Portfolio Analyzer is ready to use")
    return True

def test_simple_usage():
    """
    Test the simplest possible usage.
    """
    print("\n🔧 Testing Simple Usage")
    print("=" * 40)
    
    try:
        from sip_portfolio_analyzer import SIPPortfolioAnalyzer
        
        # Load data
        nav_data = pd.read_csv("input_data/nav_data.csv", index_col=0, parse_dates=True)
        
        # Initialize
        analyzer = SIPPortfolioAnalyzer(nav_data)
        
        # Get some funds
        funds = analyzer.get_available_funds()[:6]
        
        # Simple portfolio dictionary
        portfolios = {
            'Simple Portfolio 1': funds[:3],
            'Simple Portfolio 2': funds[3:6]
        }
        
        # Run analysis with minimal parameters
        results = analyzer.analyze_portfolios(portfolios)
        
        if not results.empty:
            print("✅ Simple usage test passed")
            print(f"📊 Generated {len(results)} portfolio results")
            
            # Show key columns
            key_cols = ['Portfolio Name', 'XIRR (%)', 'CAGR (%)']
            print("\n📈 Sample Results:")
            print(results[key_cols].to_string(index=False))
        else:
            print("❌ Simple usage test failed - no results")
            return False
            
    except Exception as e:
        print(f"❌ Error in simple usage test: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Starting SIP Portfolio Analyzer Tests")
    print("=" * 60)
    
    # Run main test
    main_test_passed = test_sip_analyzer()
    
    # Run simple usage test
    simple_test_passed = test_simple_usage()
    
    print("\n" + "=" * 60)
    if main_test_passed and simple_test_passed:
        print("🎊 ALL TESTS PASSED - SIP Portfolio Analyzer is ready!")
        print("\n📚 Next steps:")
        print("1. Run 'python sip_analyzer_example.py' for comprehensive examples")
        print("2. Use the simple dictionary format: {'Portfolio Name': ['Fund1', 'Fund2', ...]}")
        print("3. Call analyzer.analyze_portfolios() to get your comparison table")
        print("\n💡 Key Features:")
        print("✅ Equal-weighted portfolios (no rebalancing)")
        print("✅ SIP XIRR calculation")
        print("✅ Comprehensive non-SIP metrics")
        print("✅ Simple dictionary input format")
        print("✅ Direct comparison table output")
    else:
        print("❌ SOME TESTS FAILED - Please check the errors above")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure all required packages are installed")
        print("2. Check that input_data/nav_data.csv exists")
        print("3. Verify sipModules.py is in the same directory")
    
    print("=" * 60)
