"""
Excel-Based Portfolio Analyzer
=============================

This script creates portfolio combinations using the exact fund data from the
"MF_NAV\Scheme Names - Traditional SIP.xlsx" file with AUM and Vintage sheets.
Portfolio naming format: 'sheetname_category_portfolio'

Author: Augment Agent
Date: 2025-06-29
"""

import pandas as pd
import numpy as np
from datetime import datetime
from sip_portfolio_analyzer import SIPPortfolioAnalyzer

def get_aum_fund_data():
    """
    Get the exact AUM fund data as provided.
    """
    aum_data = {
        'Flexi Cap Fund': [
            'HDFC Flexi Cap Fund(G)',
            'Bandhan Flexi Cap Fund-Reg(G)',
            'Franklin India Flexi Cap Fund(G)',
            'UTI Flexi Cap Fund-Reg(G)',
            'Kotak Flexicap Fund(G)'
        ],
        'Large & Mid Cap': [
            'Nippon India Vision Fund(G)',
            'Franklin India Equity Advantage Fund(G)',
            'ICICI Pru Large & Mid Cap Fund(G)',
            'SBI Large & Midcap Fund-Reg(G)',
            'HDFC Large and Mid Cap Fund-Reg(G)'
        ],
        'Large Cap Fund': [
            'HDFC Large Cap Fund(G)',
            'Aditya Birla SL Frontline Equity Fund(G)',
            'ICICI Pru Large Cap Fund(G)',
            'Franklin India Bluechip Fund(G)',
            'DSP Large Cap Fund-Reg(G)'
        ],
        'Mid Cap Fund': [
            'HDFC Mid-Cap Opportunities Fund(G)',
            'Nippon India Growth Fund(G)',
            'Franklin India Prima Fund(G)',
            'UTI Mid Cap Fund-Reg(G)',
            'Sundaram Mid Cap Fund-Reg(G)'
        ],
        'Multi Cap Fund': [
            'Nippon India Multi Cap Fund(G)',
            'ICICI Pru Multicap Fund(G)',
            'Invesco India Multicap Fund(G)',
            'Sundaram Multi Cap Fund(G)',
            'Baroda BNP Paribas Multi Cap Fund-Reg(G)'
        ],
        'Small cap Fund': [
            'Franklin India Smaller Cos Fund(G)',
            'DSP Small Cap Fund-Reg(G)',
            'Sundaram Small Cap Fund(G)',
            'HDFC Small Cap Fund-Reg(G)',
            'SBI Small Cap Fund-Reg(G)'
        ]
    }
    return aum_data

def get_vintage_fund_data():
    """
    Get the exact Vintage fund data as provided.
    """
    vintage_data = {
        'Flexi Cap Fund': [
            'Franklin India Flexi Cap Fund(G)',
            'HDFC Flexi Cap Fund(G)',
            'Aditya Birla SL Flexi Cap Fund(G)',
            'Canara Rob Flexi Cap Fund-Reg(G)',
            'HSBC Flexi Cap Fund-Reg(G)'
        ],
        'Large & Mid Cap': [
            'Tata Large & Mid Cap Fund-Reg(G)',
            'SBI Large & Midcap Fund-Reg(G)',
            'HDFC Large and Mid Cap Fund-Reg(G)',
            'Nippon India Vision Fund(G)',
            'ICICI Pru Large & Mid Cap Fund(G)'
        ],
        'Large Cap Fund': [
            'Franklin India Bluechip Fund(G)',
            'HDFC Large Cap Fund(G)',
            'Aditya Birla SL Frontline Equity Fund(G)',
            'DSP Large Cap Fund-Reg(G)',
            'UTI Large Cap Fund-Reg(G)'
        ],
        'Mid Cap Fund': [
            'Franklin India Prima Fund(G)',
            'Nippon India Growth Fund(G)',
            'Sundaram Mid Cap Fund-Reg(G)',
            'Aditya Birla SL Midcap Fund(G)',
            'ICICI Pru Midcap Fund(G)'
        ],
        'Multi Cap Fund': [
            'ICICI Pru Multicap Fund(G)',
            'Sundaram Multi Cap Fund(G)',
            'Baroda BNP Paribas Multi Cap Fund-Reg(G)',
            'Nippon India Multi Cap Fund(G)',
            'Invesco India Multicap Fund(G)'
        ],
        'Small cap Fund': [
            'Sundaram Small Cap Fund(G)',
            'Kotak Small Cap Fund(G)',
            'Franklin India Smaller Cos Fund(G)',
            'Aditya Birla SL Small Cap Fund(G)',
            'DSP Small Cap Fund-Reg(G)'
        ]
    }
    return vintage_data

def create_excel_portfolios():
    """
    Create portfolios with the naming format: 'sheetname_category_portfolio'
    """
    portfolios_dict = {}
    
    # Get AUM data and create AUM portfolios
    aum_data = get_aum_fund_data()
    for category, funds in aum_data.items():
        portfolio_name = f"AUM_{category.replace(' ', '_').replace('&', 'and')}_portfolio"
        portfolios_dict[portfolio_name] = funds
    
    # Get Vintage data and create Vintage portfolios
    vintage_data = get_vintage_fund_data()
    for category, funds in vintage_data.items():
        portfolio_name = f"Vintage_{category.replace(' ', '_').replace('&', 'and')}_portfolio"
        portfolios_dict[portfolio_name] = funds
    
    return portfolios_dict

def main_excel_analysis():
    """
    Run comprehensive analysis using the Excel fund data.
    """
    print("🚀 Excel-Based Portfolio Analysis")
    print("Using data from: MF_NAV\\Scheme Names - Traditional SIP.xlsx")
    print("=" * 70)
    
    # Step 1: Load NAV Data
    print("\n📊 Step 1: Loading NAV Data...")
    try:
        nav_data = pd.read_csv("input_data/nav_data.csv", index_col=0, parse_dates=True)
        print(f"✅ Loaded NAV data: {nav_data.shape[0]} rows, {nav_data.shape[1]} funds")
        
        # Show available funds for reference
        print(f"\n📋 Available funds in NAV data:")
        for i, fund in enumerate(nav_data.columns[:10], 1):  # Show first 10
            print(f"  {i:2d}. {fund}")
        if len(nav_data.columns) > 10:
            print(f"  ... and {len(nav_data.columns) - 10} more funds")
            
    except FileNotFoundError:
        print("❌ Error: nav_data.csv not found in input_data folder")
        return
    except Exception as e:
        print(f"❌ Error loading NAV data: {str(e)}")
        return
    
    # Step 2: Initialize Analyzer
    print("\n🔧 Step 2: Initializing SIP Portfolio Analyzer...")
    analyzer = SIPPortfolioAnalyzer(
        nav_data=nav_data,
        start_date="2015-01-01",
        end_date="2024-08-30"
    )
    
    # Step 3: Create Portfolios from Excel Data
    print("\n🎯 Step 3: Creating Portfolios from Excel Data...")
    
    portfolios_dict = create_excel_portfolios()
    
    print(f"✅ Created {len(portfolios_dict)} portfolios from Excel data:")
    
    # Show AUM portfolios
    aum_portfolios = {k: v for k, v in portfolios_dict.items() if k.startswith('AUM_')}
    print(f"\n📊 AUM-based portfolios ({len(aum_portfolios)}):")
    for name in aum_portfolios.keys():
        print(f"  - {name}")
    
    # Show Vintage portfolios
    vintage_portfolios = {k: v for k, v in portfolios_dict.items() if k.startswith('Vintage_')}
    print(f"\n📈 Vintage-based portfolios ({len(vintage_portfolios)}):")
    for name in vintage_portfolios.keys():
        print(f"  - {name}")
    
    # Show sample portfolio structure
    print(f"\n📋 Sample Portfolio Structure:")
    sample_portfolio = list(portfolios_dict.items())[0]
    print(f"\n{sample_portfolio[0]}:")
    for fund in sample_portfolio[1]:
        print(f"  - {fund}")
    
    # Step 4: Validate Portfolios
    print(f"\n✅ Step 4: Validating Portfolios against NAV Data...")
    valid_portfolios, errors = analyzer.validate_portfolios_dict(portfolios_dict)
    
    if errors:
        print("⚠️ Validation Results:")
        print(f"  - Total portfolios: {len(portfolios_dict)}")
        print(f"  - Valid portfolios: {len(valid_portfolios)}")
        print(f"  - Portfolios with issues: {len(errors)}")
        
        # Show which funds are missing
        print(f"\n📋 Fund Matching Analysis:")
        all_excel_funds = set()
        for funds in portfolios_dict.values():
            all_excel_funds.update(funds)
        
        available_nav_funds = set(nav_data.columns)
        missing_funds = all_excel_funds - available_nav_funds
        
        print(f"  - Total unique funds in Excel: {len(all_excel_funds)}")
        print(f"  - Funds available in NAV data: {len(all_excel_funds & available_nav_funds)}")
        print(f"  - Funds missing from NAV data: {len(missing_funds)}")
        
        if missing_funds:
            print(f"\n⚠️ Missing funds (first 10):")
            for i, fund in enumerate(list(missing_funds)[:10], 1):
                print(f"  {i:2d}. {fund}")
            if len(missing_funds) > 10:
                print(f"  ... and {len(missing_funds) - 10} more")
    
    print(f"\n✅ Portfolio validation complete: {len(valid_portfolios)} portfolios ready for analysis")
    
    # Step 5: Run Analysis (if we have valid portfolios)
    if valid_portfolios:
        print(f"\n📊 Step 5: Running Analysis on Valid Portfolios...")
        
        # Run the analysis
        results = analyzer.analyze_portfolios(
            portfolios_dict=valid_portfolios,
            sip_amount=10000,                    # Monthly SIP amount
            sip_start_date="2018-01-01",         # SIP start date
            sip_end_date="2024-06-30",           # SIP end date
            benchmark_fund=None                  # No benchmark since funds don't match
        )
        
        if not results.empty:
            # Step 6: Display Results
            print(f"\n📈 Step 6: Analysis Results...")
            print(f"✅ Successfully analyzed {len(results)} portfolios")
            
            # Show key results
            key_columns = ['Portfolio Name', 'Number of Funds', 'XIRR (%)', 'CAGR (%)', 'Sharpe Ratio']
            print(f"\n📊 Key Results:")
            print(results[key_columns].to_string(index=False))
            
            # Display top performers
            analyzer.display_top_performers(results, top_n=5)
            
            # Step 7: Export Results
            print(f"\n💾 Step 7: Exporting Results...")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_filename = f"excel_fund_analysis_{timestamp}.xlsx"
            analyzer.export_comparison_table(results, excel_filename)
            
            print(f"📁 Results exported to: {excel_filename}")
            
            return results
        else:
            print("❌ Analysis failed - no results generated")
    else:
        print("❌ No valid portfolios found for analysis")
        print("\n💡 This means the fund names in the Excel file don't match the fund names in the NAV data.")
        print("   You may need to:")
        print("   1. Update the NAV data to include these mutual funds")
        print("   2. Or map the Excel fund names to available NAV fund names")
    
    return None

if __name__ == "__main__":
    print("🎊 Excel-Based Portfolio Analysis")
    print("=" * 70)
    
    # Run the main analysis
    results = main_excel_analysis()
    
    print("\n" + "=" * 70)
    print("📚 Analysis Summary:")
    print("✅ Used exact fund data from Excel file")
    print("✅ Created portfolios with naming format: 'sheetname_category_portfolio'")
    print("✅ Processed both AUM and Vintage sheet data")
    print("✅ Validated fund availability in NAV data")
    if results is not None:
        print("✅ Generated comprehensive analysis results")
        print("✅ Exported results to Excel file")
    else:
        print("⚠️ Analysis limited due to fund name mismatches")
        print("💡 Consider updating NAV data or mapping fund names")
    print("=" * 70)
