"""
SIP Portfolio Analyzer - Enhanced Framework with Excel Integration
================================================================

A comprehensive framework for analyzing multiple SIP portfolios with equal-weighted allocations.
Features automatic portfolio generation from Excel sheets (AUM/Vintage categories).

Key Features:
- Automatic portfolio generation from Excel sheets with AUM/Vintage categories
- Portfolio naming format: 'sheetname_category_portfolio'
- Equal-weighted portfolios only (no rebalancing, no optimization)
- SIP XIRR calculation as primary metric
- Comprehensive non-SIP performance metrics
- Simple dictionary input format
- Direct comparison table output

Author: Augment Agent
Date: 2025-06-29
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import warnings
from typing import Dict, List, Tuple, Optional
from scipy.optimize import newton
import matplotlib.pyplot as plt
import itertools
from collections import defaultdict

# Import existing modules
from sipModules import SIPAllocator, XIRRCalculator, FinancialMetrics, NonSipPsuedoNav, validate_sip_dates

warnings.filterwarnings("ignore")


class SIPPortfolioAnalyzer:
    """
    Streamlined analyzer for multiple SIP portfolios with equal-weighted allocations.
    """
    
    def __init__(self, nav_data: pd.DataFrame, start_date: str = None, end_date: str = None):
        """
        Initialize the SIP Portfolio Analyzer.
        
        Parameters:
        -----------
        nav_data : pd.DataFrame
            NAV data with Date index and fund columns
        start_date : str, optional
            Start date for analysis (YYYY-MM-DD format)
        end_date : str, optional
            End date for analysis (YYYY-MM-DD format)
        """
        self.nav_data = nav_data.copy()
        self.nav_data.index = pd.to_datetime(self.nav_data.index)
        
        # Filter data by date range if provided
        if start_date:
            self.nav_data = self.nav_data[self.nav_data.index >= pd.to_datetime(start_date)]
        if end_date:
            self.nav_data = self.nav_data[self.nav_data.index <= pd.to_datetime(end_date)]
            
        self.available_funds = list(self.nav_data.columns)
        
        print(f"✅ SIP Portfolio Analyzer initialized")
        print(f"📊 Available funds: {len(self.available_funds)}")
        print(f"📅 Data period: {self.nav_data.index[0].strftime('%Y-%m-%d')} to {self.nav_data.index[-1].strftime('%Y-%m-%d')}")
    
    def generate_sip_dates(self, start_date: str, end_date: str, frequency: str = 'monthly') -> List[pd.Timestamp]:
        """Generate SIP investment dates."""
        start = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        
        if frequency == 'monthly':
            dates = pd.date_range(start=start, end=end, freq='MS')  # Month start
        elif frequency == 'quarterly':
            dates = pd.date_range(start=start, end=end, freq='QS')  # Quarter start
        else:
            raise ValueError("Frequency must be 'monthly' or 'quarterly'")
            
        return dates.tolist()
    
    def calculate_portfolio_metrics(self, portfolio_name: str, fund_list: List[str], 
                                  sip_amount: float, sip_start_date: str, sip_end_date: str,
                                  benchmark_fund: str = None) -> Dict:
        """
        Calculate comprehensive metrics for a single portfolio.
        
        Parameters:
        -----------
        portfolio_name : str
            Name of the portfolio
        fund_list : List[str]
            List of fund names in the portfolio
        sip_amount : float
            Monthly SIP amount
        sip_start_date : str
            SIP start date (YYYY-MM-DD)
        sip_end_date : str
            SIP end date (YYYY-MM-DD)
        benchmark_fund : str, optional
            Benchmark fund for comparison
            
        Returns:
        --------
        Dict : Complete metrics for the portfolio
        """
        # Validate funds exist
        missing_funds = [fund for fund in fund_list if fund not in self.available_funds]
        if missing_funds:
            print(f"⚠️ Warning: {portfolio_name} - Missing funds: {missing_funds}")
            fund_list = [fund for fund in fund_list if fund in self.available_funds]
            if not fund_list:
                return None
        
        # Create equal weights
        n_funds = len(fund_list)
        weights = {fund: 1.0 / n_funds for fund in fund_list}
        
        # Get portfolio NAV data
        portfolio_nav_data = self.nav_data[fund_list].dropna()
        
        # === SIP ANALYSIS ===
        try:
            # Generate and validate SIP dates
            sip_dates = self.generate_sip_dates(sip_start_date, sip_end_date)
            validated_sip_dates = validate_sip_dates(sip_dates, portfolio_nav_data)
            
            # Calculate SIP allocations
            sip_allocator = SIPAllocator(portfolio_nav_data, validated_sip_dates, weights, sip_amount)
            nav_data_filtered, weights_used, units_df = sip_allocator.allocate_units()
            
            # Calculate portfolio value over time
            portfolio_value = (units_df * nav_data_filtered).sum(axis=1)
            
            # Prepare cash flows for XIRR
            cash_flows = []
            for date in validated_sip_dates:
                if date in portfolio_nav_data.index:
                    cash_flows.append({'date': date, 'value': -sip_amount})
            
            # Add final value
            final_date = portfolio_value.index[-1]
            final_value = portfolio_value.iloc[-1]
            cash_flows.append({'date': final_date, 'value': final_value})
            
            # Calculate XIRR
            try:
                xirr_calc = XIRRCalculator(cash_flows)
                xirr = xirr_calc.compute() * 100  # Convert to percentage
            except:
                xirr = np.nan
            
            # SIP summary metrics
            total_invested = sip_amount * len(validated_sip_dates)
            absolute_return = final_value - total_invested
            return_percentage = (absolute_return / total_invested) * 100 if total_invested > 0 else 0
            investment_period = (pd.to_datetime(sip_end_date) - pd.to_datetime(sip_start_date)).days / 365.25
            
        except Exception as e:
            print(f"❌ Error in SIP analysis for {portfolio_name}: {str(e)}")
            xirr = np.nan
            total_invested = np.nan
            final_value = np.nan
            absolute_return = np.nan
            return_percentage = np.nan
            investment_period = np.nan
        
        # === NON-SIP ANALYSIS ===
        try:
            # Create non-SIP pseudo NAV
            non_sip_nav = NonSipPsuedoNav(portfolio_nav_data, weights, initial_corpus=100)
            portfolio_df = non_sip_nav.build_portfolio()
            portfolio_nav_series = non_sip_nav.get_adjusted_nav_series()
            
            # Get benchmark data if provided
            benchmark_nav = None
            if benchmark_fund and benchmark_fund in self.nav_data.columns:
                benchmark_nav = self.nav_data[benchmark_fund].dropna()
                # Align benchmark with portfolio dates
                common_dates = portfolio_nav_series.index.intersection(benchmark_nav.index)
                benchmark_nav = benchmark_nav.loc[common_dates]
                portfolio_nav_series = portfolio_nav_series.loc[common_dates]
            
            # Calculate comprehensive metrics
            metrics_calc = FinancialMetrics(nav_df=portfolio_nav_series, benchmark_nav=benchmark_nav)
            non_sip_metrics = metrics_calc.generate_metrics()
            
        except Exception as e:
            print(f"❌ Error in non-SIP analysis for {portfolio_name}: {str(e)}")
            non_sip_metrics = {}
        
        # Combine all metrics
        combined_metrics = {
            'Portfolio Name': portfolio_name,
            'Funds': ', '.join(fund_list),
            'Number of Funds': len(fund_list),
            'Equal Weight per Fund (%)': round(100 / len(fund_list), 2),
            
            # SIP Metrics
            'SIP Amount (₹)': sip_amount,
            'Investment Period (Years)': round(investment_period, 2) if not pd.isna(investment_period) else np.nan,
            'Total Invested (₹)': round(total_invested, 0) if not pd.isna(total_invested) else np.nan,
            'Final Value (₹)': round(final_value, 0) if not pd.isna(final_value) else np.nan,
            'Absolute Return (₹)': round(absolute_return, 0) if not pd.isna(absolute_return) else np.nan,
            'SIP Return (%)': round(return_percentage, 2) if not pd.isna(return_percentage) else np.nan,
            'XIRR (%)': round(xirr, 2) if not pd.isna(xirr) else np.nan,
            
            # Non-SIP Performance Metrics
            'CAGR (%)': round(non_sip_metrics.get('CAGR', np.nan) * 100, 2) if not pd.isna(non_sip_metrics.get('CAGR', np.nan)) else np.nan,
            'MDD (%)': round(non_sip_metrics.get('MDD', np.nan) * 100, 2) if not pd.isna(non_sip_metrics.get('MDD', np.nan)) else np.nan,
            'Avg Top 5 DD (%)': round(non_sip_metrics.get('Avg_Top_5_DD', np.nan) * 100, 2) if not pd.isna(non_sip_metrics.get('Avg_Top_5_DD', np.nan)) else np.nan,
            'Sharpe Ratio': round(non_sip_metrics.get('Sharpe', np.nan), 3) if not pd.isna(non_sip_metrics.get('Sharpe', np.nan)) else np.nan,
            'Sortino Ratio': round(non_sip_metrics.get('Sortino', np.nan), 3) if not pd.isna(non_sip_metrics.get('Sortino', np.nan)) else np.nan,
            'Rolling 1Y Volatility (%)': round(non_sip_metrics.get('Rolling_1Y_Volatility', np.nan) * 100, 2) if not pd.isna(non_sip_metrics.get('Rolling_1Y_Volatility', np.nan)) else np.nan,
            'Jensen Alpha (%)': round(non_sip_metrics.get('Jensen Alpha', np.nan) * 100, 2) if not pd.isna(non_sip_metrics.get('Jensen Alpha', np.nan)) else np.nan,
            'Upside Capture Ratio': round(non_sip_metrics.get('Upside Capture Ratio', np.nan), 3) if not pd.isna(non_sip_metrics.get('Upside Capture Ratio', np.nan)) else np.nan,
            'Downside Capture Ratio': round(non_sip_metrics.get('Downside Capture Ratio', np.nan), 3) if not pd.isna(non_sip_metrics.get('Downside Capture Ratio', np.nan)) else np.nan,
        }
        
        return combined_metrics
    
    def analyze_portfolios(self, portfolios_dict: Dict[str, List[str]], 
                          sip_amount: float = 10000,
                          sip_start_date: str = None,
                          sip_end_date: str = None,
                          benchmark_fund: str = None) -> pd.DataFrame:
        """
        Analyze multiple portfolios and return comparison table.
        
        Parameters:
        -----------
        portfolios_dict : Dict[str, List[str]]
            Dictionary with portfolio names as keys and fund lists as values
        sip_amount : float
            Monthly SIP amount (default: 10000)
        sip_start_date : str
            SIP start date (default: data start + 1 year)
        sip_end_date : str
            SIP end date (default: data end)
        benchmark_fund : str, optional
            Benchmark fund for comparison
            
        Returns:
        --------
        pd.DataFrame : Comprehensive comparison table
        """
        # Set default dates if not provided
        if not sip_start_date:
            sip_start_date = (self.nav_data.index[0] + relativedelta(years=1)).strftime('%Y-%m-%d')
        if not sip_end_date:
            sip_end_date = self.nav_data.index[-1].strftime('%Y-%m-%d')
        
        print(f"\n🚀 Analyzing {len(portfolios_dict)} portfolios...")
        print(f"💰 SIP Amount: ₹{sip_amount:,} per month")
        print(f"📅 SIP Period: {sip_start_date} to {sip_end_date}")
        if benchmark_fund:
            print(f"📊 Benchmark: {benchmark_fund}")
        
        results = []
        
        for i, (portfolio_name, fund_list) in enumerate(portfolios_dict.items(), 1):
            print(f"\n📊 Analyzing Portfolio {i}/{len(portfolios_dict)}: {portfolio_name}")
            print(f"   Funds ({len(fund_list)}): {', '.join(fund_list[:3])}{'...' if len(fund_list) > 3 else ''}")
            
            try:
                metrics = self.calculate_portfolio_metrics(
                    portfolio_name=portfolio_name,
                    fund_list=fund_list,
                    sip_amount=sip_amount,
                    sip_start_date=sip_start_date,
                    sip_end_date=sip_end_date,
                    benchmark_fund=benchmark_fund
                )
                
                if metrics:
                    results.append(metrics)
                    print(f"   ✅ XIRR: {metrics['XIRR (%)']}% | CAGR: {metrics['CAGR (%)']}% | Sharpe: {metrics['Sharpe Ratio']}")
                else:
                    print(f"   ❌ Analysis failed - no valid funds")
                    
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
                continue
        
        if not results:
            print("❌ No portfolios were successfully analyzed")
            return pd.DataFrame()
        
        # Create comparison DataFrame
        comparison_df = pd.DataFrame(results)
        
        # Sort by XIRR (descending) - handle NaN values
        comparison_df = comparison_df.sort_values('XIRR (%)', ascending=False)
        comparison_df = comparison_df.reset_index(drop=True)
        
        print(f"\n🎉 Analysis completed! {len(results)} portfolios analyzed successfully.")

        return comparison_df

    def export_comparison_table(self, comparison_df: pd.DataFrame, filename: str = None) -> str:
        """
        Export comparison table to Excel.

        Parameters:
        -----------
        comparison_df : pd.DataFrame
            Comparison table from analyze_portfolios
        filename : str, optional
            Output filename (default: auto-generated)

        Returns:
        --------
        str : Path to exported file
        """
        if comparison_df.empty:
            print("❌ No data to export")
            return None

        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"sip_portfolio_comparison_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:
                # Main comparison table
                comparison_df.to_excel(writer, sheet_name='Portfolio_Comparison', index=False)

                # Get workbook and worksheet
                workbook = writer.book
                worksheet = writer.sheets['Portfolio_Comparison']

                # Add formatting
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'top',
                    'fg_color': '#D7E4BC',
                    'border': 1
                })

                # Format headers
                for col_num, value in enumerate(comparison_df.columns.values):
                    worksheet.write(0, col_num, value, header_format)

                # Auto-adjust column widths
                for i, col in enumerate(comparison_df.columns):
                    max_len = max(
                        comparison_df[col].astype(str).map(len).max(),
                        len(str(col))
                    ) + 2
                    worksheet.set_column(i, i, min(max_len, 30))

                # Add summary statistics sheet
                summary_stats = self._generate_summary_stats(comparison_df)
                summary_stats.to_excel(writer, sheet_name='Summary_Statistics', index=True)

            print(f"📁 Comparison table exported to: {filename}")
            return filename

        except Exception as e:
            print(f"❌ Error exporting file: {str(e)}")
            return None

    def _generate_summary_stats(self, comparison_df: pd.DataFrame) -> pd.DataFrame:
        """Generate summary statistics for the comparison table."""
        numeric_cols = ['XIRR (%)', 'CAGR (%)', 'MDD (%)', 'Sharpe Ratio', 'Sortino Ratio']

        stats_data = {}
        for col in numeric_cols:
            if col in comparison_df.columns:
                col_data = pd.to_numeric(comparison_df[col], errors='coerce')
                stats_data[col] = {
                    'Mean': round(col_data.mean(), 2),
                    'Median': round(col_data.median(), 2),
                    'Std Dev': round(col_data.std(), 2),
                    'Min': round(col_data.min(), 2),
                    'Max': round(col_data.max(), 2),
                    'Count': col_data.count()
                }

        return pd.DataFrame(stats_data).T

    def display_top_performers(self, comparison_df: pd.DataFrame, top_n: int = 5) -> None:
        """
        Display top performing portfolios by key metrics.

        Parameters:
        -----------
        comparison_df : pd.DataFrame
            Comparison table from analyze_portfolios
        top_n : int
            Number of top performers to show
        """
        if comparison_df.empty:
            print("❌ No data to display")
            return

        print(f"\n🏆 TOP {top_n} PERFORMERS")
        print("=" * 60)

        # Top by XIRR
        print(f"\n📈 Top {top_n} by XIRR:")
        top_xirr = comparison_df.nlargest(top_n, 'XIRR (%)')[['Portfolio Name', 'XIRR (%)', 'Number of Funds']]
        for idx, row in top_xirr.iterrows():
            print(f"  {idx+1}. {row['Portfolio Name']}: {row['XIRR (%)']}% ({row['Number of Funds']} funds)")

        # Top by CAGR
        print(f"\n📊 Top {top_n} by CAGR:")
        top_cagr = comparison_df.nlargest(top_n, 'CAGR (%)')[['Portfolio Name', 'CAGR (%)', 'Number of Funds']]
        for idx, row in top_cagr.iterrows():
            print(f"  {idx+1}. {row['Portfolio Name']}: {row['CAGR (%)']}% ({row['Number of Funds']} funds)")

        # Top by Sharpe Ratio
        print(f"\n⚖️ Top {top_n} by Sharpe Ratio:")
        top_sharpe = comparison_df.nlargest(top_n, 'Sharpe Ratio')[['Portfolio Name', 'Sharpe Ratio', 'Number of Funds']]
        for idx, row in top_sharpe.iterrows():
            print(f"  {idx+1}. {row['Portfolio Name']}: {row['Sharpe Ratio']} ({row['Number of Funds']} funds)")

        # Best MDD (least negative)
        print(f"\n🛡️ Best {top_n} by MDD (lowest drawdown):")
        best_mdd = comparison_df.nlargest(top_n, 'MDD (%)')[['Portfolio Name', 'MDD (%)', 'Number of Funds']]
        for idx, row in best_mdd.iterrows():
            print(f"  {idx+1}. {row['Portfolio Name']}: {row['MDD (%)']}% ({row['Number of Funds']} funds)")

    def plot_comparison(self, comparison_df: pd.DataFrame, metric: str = 'XIRR (%)',
                       top_n: int = 10, save_plot: bool = True) -> None:
        """
        Plot comparison of portfolios by specified metric.

        Parameters:
        -----------
        comparison_df : pd.DataFrame
            Comparison table from analyze_portfolios
        metric : str
            Metric to compare
        top_n : int
            Number of top portfolios to show
        save_plot : bool
            Whether to save the plot
        """
        if comparison_df.empty or metric not in comparison_df.columns:
            print(f"❌ Cannot plot: {metric} not found in data")
            return

        # Get top N portfolios
        if 'MDD' in metric or 'DD' in metric:
            # For drawdown metrics, higher (less negative) is better
            plot_data = comparison_df.nlargest(top_n, metric)
        else:
            # For other metrics, higher is better
            plot_data = comparison_df.nlargest(top_n, metric)

        # Create plot
        plt.figure(figsize=(14, 8))
        bars = plt.bar(range(len(plot_data)), plot_data[metric],
                      color='steelblue', alpha=0.7, edgecolor='navy', linewidth=0.5)

        plt.title(f'Top {top_n} Portfolios by {metric}', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('Portfolio', fontsize=12)
        plt.ylabel(metric, fontsize=12)
        plt.xticks(range(len(plot_data)), plot_data['Portfolio Name'], rotation=45, ha='right')
        plt.grid(axis='y', alpha=0.3)

        # Add value labels on bars
        for i, bar in enumerate(bars):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.2f}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()

        if save_plot:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_filename = f"portfolio_comparison_{metric.replace(' ', '_').replace('(', '').replace(')', '')}_{timestamp}.png"
            plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
            print(f"📊 Plot saved as: {plot_filename}")

        plt.show()

    def get_available_funds(self) -> List[str]:
        """Get list of available funds in the dataset."""
        return self.available_funds.copy()

    def validate_portfolios_dict(self, portfolios_dict: Dict[str, List[str]]) -> Tuple[Dict[str, List[str]], List[str]]:
        """
        Validate portfolio dictionary.

        Parameters:
        -----------
        portfolios_dict : Dict[str, List[str]]
            Portfolio dictionary to validate

        Returns:
        --------
        Tuple[Dict[str, List[str]], List[str]] : Valid portfolios and error messages
        """
        valid_portfolios = {}
        errors = []

        for portfolio_name, fund_list in portfolios_dict.items():
            if not isinstance(fund_list, list) or len(fund_list) == 0:
                errors.append(f"Portfolio '{portfolio_name}': Fund list must be a non-empty list")
                continue

            missing_funds = [fund for fund in fund_list if fund not in self.available_funds]
            if missing_funds:
                errors.append(f"Portfolio '{portfolio_name}': Funds not found: {missing_funds}")
                # Keep valid funds
                valid_funds = [fund for fund in fund_list if fund in self.available_funds]
                if valid_funds:
                    valid_portfolios[portfolio_name] = valid_funds
            else:
                valid_portfolios[portfolio_name] = fund_list

        return valid_portfolios, errors

    def load_excel_portfolios(self, excel_file_path: str,
                             min_funds_per_category: int = 3,
                             max_funds_per_category: int = 10) -> Dict[str, List[str]]:
        """
        Load portfolio combinations from Excel file with AUM and Vintage sheets.

        Parameters:
        -----------
        excel_file_path : str
            Path to Excel file with AUM and Vintage sheets
        min_funds_per_category : int
            Minimum number of funds required per category (default: 3)
        max_funds_per_category : int
            Maximum number of funds to include per category (default: 10)

        Returns:
        --------
        Dict[str, List[str]] : Portfolio dictionary with naming format 'sheetname_category_portfolio'
        """
        portfolios_dict = {}

        try:
            # Read AUM sheet
            print("📊 Loading AUM sheet...")
            aum_df = pd.read_excel(excel_file_path, sheet_name='AUM', engine='openpyxl', header=1)

            # Clean column names for AUM
            aum_df.columns = ['Index', 'Scheme Name', 'Category', 'Inception Date', 'AUM'] + \
                           [f'Col_{i}' for i in range(5, len(aum_df.columns))]
            aum_df = aum_df.dropna(subset=['Scheme Name'])
            aum_df = aum_df[aum_df['Scheme Name'] != 'Scheme Name']  # Remove header duplicates

            print(f"✅ AUM sheet loaded: {len(aum_df)} funds")

            # Read Vintage sheet
            print("📊 Loading Vintage sheet...")
            vintage_df = pd.read_excel(excel_file_path, sheet_name='Vintage', engine='openpyxl', header=0)

            # Clean column names for Vintage
            vintage_df.columns = ['Scheme Name', 'Category', 'Inception Date', 'Age'] + \
                               [f'Col_{i}' for i in range(4, len(vintage_df.columns))]
            vintage_df = vintage_df.dropna(subset=['Scheme Name'])
            vintage_df = vintage_df[vintage_df['Scheme Name'] != 'Scheme Name']  # Remove header duplicates

            print(f"✅ Vintage sheet loaded: {len(vintage_df)} funds")

            # Process AUM sheet
            aum_portfolios = self._create_category_portfolios(
                aum_df, 'AUM', min_funds_per_category, max_funds_per_category
            )
            portfolios_dict.update(aum_portfolios)

            # Process Vintage sheet
            vintage_portfolios = self._create_category_portfolios(
                vintage_df, 'Vintage', min_funds_per_category, max_funds_per_category
            )
            portfolios_dict.update(vintage_portfolios)

            print(f"\n🎯 Generated {len(portfolios_dict)} portfolios from Excel sheets")

            return portfolios_dict

        except Exception as e:
            print(f"❌ Error loading Excel portfolios: {str(e)}")
            return {}

    def _create_category_portfolios(self, df: pd.DataFrame, sheet_name: str,
                                  min_funds: int, max_funds: int) -> Dict[str, List[str]]:
        """
        Create portfolios from a dataframe by category.

        Parameters:
        -----------
        df : pd.DataFrame
            DataFrame with 'Scheme Name' and 'Category' columns
        sheet_name : str
            Name of the sheet (AUM or Vintage)
        min_funds : int
            Minimum funds per category
        max_funds : int
            Maximum funds per category

        Returns:
        --------
        Dict[str, List[str]] : Category-based portfolios
        """
        portfolios = {}

        # Group by category
        categories = df['Category'].dropna().unique()

        print(f"\n📋 Processing {sheet_name} sheet categories:")

        for category in categories:
            # Get funds for this category
            category_funds = df[df['Category'] == category]['Scheme Name'].tolist()

            # Filter out any NaN values
            category_funds = [fund for fund in category_funds if pd.notna(fund) and fund != '']

            if len(category_funds) >= min_funds:
                # Limit to max_funds if specified
                if len(category_funds) > max_funds:
                    # For AUM: take top funds by AUM value
                    if sheet_name == 'AUM' and 'AUM' in df.columns:
                        category_df = df[df['Category'] == category].copy()
                        category_df = category_df.nlargest(max_funds, 'AUM')
                        category_funds = category_df['Scheme Name'].tolist()
                    # For Vintage: take oldest funds (highest age)
                    elif sheet_name == 'Vintage' and 'Age' in df.columns:
                        category_df = df[df['Category'] == category].copy()
                        category_df = category_df.nlargest(max_funds, 'Age')
                        category_funds = category_df['Scheme Name'].tolist()
                    else:
                        # Fallback: take first max_funds
                        category_funds = category_funds[:max_funds]

                # Create portfolio name: sheetname_category_portfolio
                portfolio_name = f"{sheet_name}_{category.replace(' ', '_').replace('&', 'and')}_portfolio"
                portfolios[portfolio_name] = category_funds

                print(f"  ✅ {portfolio_name}: {len(category_funds)} funds")
            else:
                print(f"  ⚠️ {category}: Only {len(category_funds)} funds (minimum {min_funds} required)")

        return portfolios

    def run_excel_portfolio_analysis(self, excel_file_path: str,
                                   sip_amount: float = 10000,
                                   sip_start_date: str = None,
                                   sip_end_date: str = None,
                                   benchmark_fund: str = None,
                                   min_funds_per_category: int = 3,
                                   max_funds_per_category: int = 10) -> pd.DataFrame:
        """
        Complete analysis pipeline: Load Excel portfolios and run comprehensive analysis.

        Parameters:
        -----------
        excel_file_path : str
            Path to Excel file with AUM and Vintage sheets
        sip_amount : float
            Monthly SIP amount (default: 10000)
        sip_start_date : str
            SIP start date (default: data start + 1 year)
        sip_end_date : str
            SIP end date (default: data end)
        benchmark_fund : str, optional
            Benchmark fund for comparison
        min_funds_per_category : int
            Minimum funds per category (default: 3)
        max_funds_per_category : int
            Maximum funds per category (default: 10)

        Returns:
        --------
        pd.DataFrame : Complete comparison table with all portfolio results
        """
        print("🚀 Starting Excel Portfolio Analysis Pipeline")
        print("=" * 60)

        # Step 1: Load portfolios from Excel
        portfolios_dict = self.load_excel_portfolios(
            excel_file_path, min_funds_per_category, max_funds_per_category
        )

        if not portfolios_dict:
            print("❌ No portfolios loaded from Excel file")
            return pd.DataFrame()

        # Step 2: Validate portfolios against available NAV data
        print(f"\n🔍 Validating {len(portfolios_dict)} portfolios against NAV data...")
        valid_portfolios, errors = self.validate_portfolios_dict(portfolios_dict)

        if errors:
            print("⚠️ Validation warnings:")
            for error in errors[:5]:  # Show first 5 errors
                print(f"  - {error}")
            if len(errors) > 5:
                print(f"  ... and {len(errors) - 5} more warnings")

        print(f"✅ {len(valid_portfolios)} portfolios validated successfully")

        if not valid_portfolios:
            print("❌ No valid portfolios found")
            return pd.DataFrame()

        # Step 3: Run comprehensive analysis
        print(f"\n📊 Running comprehensive analysis...")
        comparison_table = self.analyze_portfolios(
            portfolios_dict=valid_portfolios,
            sip_amount=sip_amount,
            sip_start_date=sip_start_date,
            sip_end_date=sip_end_date,
            benchmark_fund=benchmark_fund
        )

        if comparison_table.empty:
            print("❌ Analysis failed - no results generated")
            return pd.DataFrame()

        # Step 4: Display summary results
        print(f"\n📈 Analysis Summary:")
        print(f"  - Total portfolios analyzed: {len(comparison_table)}")
        print(f"  - SIP Amount: ₹{sip_amount:,} per month")
        print(f"  - Analysis period: {sip_start_date or 'Auto'} to {sip_end_date or 'Auto'}")

        # Show top performers
        self.display_top_performers(comparison_table, top_n=5)

        # Step 5: Export results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        excel_filename = f"excel_portfolio_analysis_{timestamp}.xlsx"
        self.export_comparison_table(comparison_table, excel_filename)

        print(f"\n🎉 Excel Portfolio Analysis Complete!")
        print(f"📁 Results saved to: {excel_filename}")

        return comparison_table

    def create_custom_combinations(self, portfolios_dict: Dict[str, List[str]],
                                 combination_size: int = 3) -> Dict[str, List[str]]:
        """
        Create additional portfolio combinations by mixing funds across categories.

        Parameters:
        -----------
        portfolios_dict : Dict[str, List[str]]
            Base portfolios dictionary
        combination_size : int
            Number of funds per combination portfolio

        Returns:
        --------
        Dict[str, List[str]] : Additional combination portfolios
        """
        print(f"\n🔄 Creating custom combinations with {combination_size} funds each...")

        # Get all unique funds
        all_funds = []
        category_funds = {}

        for portfolio_name, fund_list in portfolios_dict.items():
            all_funds.extend(fund_list)
            # Extract category from portfolio name
            if '_portfolio' in portfolio_name:
                category = portfolio_name.split('_')[1:-1]  # Remove sheet name and 'portfolio'
                category = '_'.join(category)
                if category not in category_funds:
                    category_funds[category] = []
                category_funds[category].extend(fund_list)

        # Remove duplicates
        all_funds = list(set(all_funds))
        for category in category_funds:
            category_funds[category] = list(set(category_funds[category]))

        combination_portfolios = {}

        # Create cross-category combinations
        categories = list(category_funds.keys())
        if len(categories) >= 2:
            for i, cat1 in enumerate(categories):
                for cat2 in categories[i+1:]:
                    # Take funds from both categories
                    funds_cat1 = category_funds[cat1][:combination_size//2]
                    funds_cat2 = category_funds[cat2][:combination_size - len(funds_cat1)]

                    if len(funds_cat1) + len(funds_cat2) >= combination_size:
                        combo_name = f"Combo_{cat1}_and_{cat2}_portfolio"
                        combination_portfolios[combo_name] = funds_cat1 + funds_cat2

        print(f"✅ Created {len(combination_portfolios)} combination portfolios")

        return combination_portfolios
