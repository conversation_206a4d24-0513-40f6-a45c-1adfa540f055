"""
SIP Portfolio Analyzer - Example Usage
=====================================

This example demonstrates how to use the streamlined SIP Portfolio Analyzer.
Input: Dictionary with portfolio names as keys and fund lists as values
Output: Comprehensive comparison table with XIRR and non-SIP metrics

Author: Augment Agent
Date: 2025-06-29
"""

import pandas as pd
import numpy as np
from datetime import datetime
from sip_portfolio_analyzer import SIPPortfolioAnalyzer

def main_example():
    """
    Main example demonstrating the SIP Portfolio Analyzer.
    """
    print("🚀 SIP Portfolio Analyzer - Example Usage")
    print("=" * 60)
    
    # Step 1: Load NAV Data
    print("\n📊 Step 1: Loading NAV Data...")
    try:
        nav_data = pd.read_csv("input_data/nav_data.csv", index_col=0, parse_dates=True)
        print(f"✅ Loaded NAV data: {nav_data.shape[0]} rows, {nav_data.shape[1]} funds")
    except FileNotFoundError:
        print("❌ Error: nav_data.csv not found in input_data folder")
        return
    except Exception as e:
        print(f"❌ Error loading data: {str(e)}")
        return
    
    # Step 2: Initialize Analyzer
    print("\n🔧 Step 2: Initializing SIP Portfolio Analyzer...")
    analyzer = SIPPortfolioAnalyzer(
        nav_data=nav_data,
        start_date="2015-01-01",  # Optional: filter start date
        end_date="2024-12-31"     # Optional: filter end date
    )
    
    # Step 3: View Available Funds
    print("\n📋 Step 3: Available Funds...")
    available_funds = analyzer.get_available_funds()
    print(f"Total available funds: {len(available_funds)}")
    print("Sample funds:", available_funds[:5])
    
    # Step 4: Define Portfolio Dictionary
    print("\n🎯 Step 4: Defining Portfolio Dictionary...")
    
    # Simple dictionary format: Portfolio Name -> List of Funds
    portfolios_dict = {
        'Large Cap Focus': [
            'Nifty 50',
            'Nifty 100',
            'NIFTY TOP 10 EQUAL WEIGHT',
            'Nifty 50 Equal Weight'
        ],
        
        'Mid & Small Cap': [
            'Nifty Midcap 150',
            'Nifty Midcap 100',
            'Nifty Smallcap 250',
            'Nifty Microcap 250'
        ],
        
        'Quality Strategy': [
            'Nifty 100 Quality 30',
            'Nifty 200 Quality 30',
            'NIFTY DIVIDEND OPPORTUNITIES 50'
        ],
        
        'Momentum Strategy': [
            'Nifty 500 Momentum 50',
            'Nifty 200 Momentum 30',
            'NIFTY SMALLCAP250 MOMENTUM QUALITY 100'
        ],
        
        'Value Strategy': [
            'Nifty 500 Value 50',
            'Nifty 200 Value 30'
        ],
        
        'Diversified Broad': [
            'Nifty 500',
            'Nifty 500 Equal Weight',
            'Nifty LargeMidCap 250',
            'Nifty Alpha 50'
        ],
        
        'Low Volatility': [
            'Nifty Alpha Low Vol 30',
            'Nifty 100 Equal Weight',
            'Nifty 200 Quality 30'
        ],
        
        'Multi-Cap Blend': [
            'Nifty 50',
            'Nifty Midcap 150',
            'Nifty Smallcap 250',
            'Nifty 500',
            'Nifty LargeMidCap 250'
        ]
    }
    
    print(f"✅ Defined {len(portfolios_dict)} portfolios:")
    for name, funds in portfolios_dict.items():
        print(f"  - {name}: {len(funds)} funds")
    
    # Step 5: Validate Portfolio Dictionary
    print("\n✅ Step 5: Validating Portfolio Dictionary...")
    valid_portfolios, errors = analyzer.validate_portfolios_dict(portfolios_dict)
    
    if errors:
        print("⚠️ Validation Errors:")
        for error in errors:
            print(f"  - {error}")
    
    print(f"✅ {len(valid_portfolios)} out of {len(portfolios_dict)} portfolios are valid")
    
    # Step 6: Run Analysis
    print("\n🔍 Step 6: Running Portfolio Analysis...")
    
    # Analysis parameters
    sip_amount = 10000  # Monthly SIP amount
    sip_start_date = "2018-01-01"  # SIP start date
    sip_end_date = "2024-06-30"    # SIP end date
    benchmark_fund = "Nifty 50"    # Benchmark for comparison
    
    # Run the analysis - This is the main function call!
    comparison_table = analyzer.analyze_portfolios(
        portfolios_dict=valid_portfolios,
        sip_amount=sip_amount,
        sip_start_date=sip_start_date,
        sip_end_date=sip_end_date,
        benchmark_fund=benchmark_fund
    )
    
    # Step 7: Display Results
    print("\n📈 Step 7: Analysis Results...")
    if not comparison_table.empty:
        print(f"✅ Successfully analyzed {len(comparison_table)} portfolios")
        
        # Display key columns
        key_columns = ['Portfolio Name', 'Number of Funds', 'XIRR (%)', 'CAGR (%)', 'Sharpe Ratio', 'MDD (%)']
        print("\n📊 Key Results:")
        print(comparison_table[key_columns].to_string(index=False))
        
        # Display top performers
        analyzer.display_top_performers(comparison_table, top_n=3)
        
    else:
        print("❌ No results to display")
        return
    
    # Step 8: Export Results
    print("\n💾 Step 8: Exporting Results...")
    excel_file = analyzer.export_comparison_table(comparison_table)
    
    # Step 9: Generate Plots
    print("\n📊 Step 9: Generating Comparison Plots...")
    
    # Plot XIRR comparison
    analyzer.plot_comparison(comparison_table, metric='XIRR (%)', top_n=len(comparison_table))
    
    # Plot CAGR comparison
    analyzer.plot_comparison(comparison_table, metric='CAGR (%)', top_n=len(comparison_table))
    
    # Plot Sharpe Ratio comparison
    analyzer.plot_comparison(comparison_table, metric='Sharpe Ratio', top_n=len(comparison_table))
    
    print("\n🎉 Analysis Complete!")
    print("=" * 60)
    print("📁 Check the Excel file for detailed results")
    print("📊 Check the PNG files for comparison plots")
    
    return comparison_table

def simple_example():
    """
    Simple example with minimal configuration.
    """
    print("\n" + "="*60)
    print("🎯 Simple Example - Minimal Configuration")
    print("="*60)
    
    # Load data
    nav_data = pd.read_csv("input_data/nav_data.csv", index_col=0, parse_dates=True)
    
    # Initialize analyzer
    analyzer = SIPPortfolioAnalyzer(nav_data)
    
    # Define simple portfolios
    simple_portfolios = {
        'Conservative': ['Nifty 50', 'Nifty 100', 'NIFTY DIVIDEND OPPORTUNITIES 50'],
        'Aggressive': ['Nifty Smallcap 250', 'Nifty Microcap 250', 'Nifty 500 Momentum 50'],
        'Balanced': ['Nifty 500', 'Nifty LargeMidCap 250', 'Nifty 500 Equal Weight']
    }
    
    # Run analysis with default parameters
    results = analyzer.analyze_portfolios(simple_portfolios)
    
    # Display results
    if not results.empty:
        print("\n📊 Simple Analysis Results:")
        key_cols = ['Portfolio Name', 'XIRR (%)', 'CAGR (%)', 'Sharpe Ratio']
        print(results[key_cols].to_string(index=False))
        
        # Export results
        analyzer.export_comparison_table(results, "simple_portfolio_analysis.xlsx")
    
    return results

def custom_parameters_example():
    """
    Example with custom analysis parameters.
    """
    print("\n" + "="*60)
    print("🔧 Custom Parameters Example")
    print("="*60)
    
    # Load data
    nav_data = pd.read_csv("input_data/nav_data.csv", index_col=0, parse_dates=True)
    
    # Initialize analyzer with date filtering
    analyzer = SIPPortfolioAnalyzer(
        nav_data=nav_data,
        start_date="2020-01-01",  # Filter data from 2020
        end_date="2024-06-30"     # Filter data until June 2024
    )
    
    # Define portfolios
    custom_portfolios = {
        'Tech & Growth': ['Nifty 500 Momentum 50', 'Nifty Alpha 50'],
        'Defensive': ['Nifty 100 Quality 30', 'NIFTY DIVIDEND OPPORTUNITIES 50'],
        'Broad Market': ['Nifty 500', 'Nifty 500 Equal Weight']
    }
    
    # Run analysis with custom parameters
    results = analyzer.analyze_portfolios(
        portfolios_dict=custom_portfolios,
        sip_amount=15000,  # Higher SIP amount
        sip_start_date="2021-01-01",  # Later start date
        sip_end_date="2024-06-30",
        benchmark_fund="Nifty 500"  # Different benchmark
    )
    
    # Display and export results
    if not results.empty:
        analyzer.display_top_performers(results, top_n=3)
        analyzer.export_comparison_table(results, "custom_parameters_analysis.xlsx")
    
    return results

if __name__ == "__main__":
    print("🎊 SIP Portfolio Analyzer - Multiple Examples")
    print("=" * 70)
    
    # Run main comprehensive example
    print("\n1️⃣ COMPREHENSIVE EXAMPLE")
    main_results = main_example()
    
    # Run simple example
    print("\n2️⃣ SIMPLE EXAMPLE")
    simple_results = simple_example()
    
    # Run custom parameters example
    print("\n3️⃣ CUSTOM PARAMETERS EXAMPLE")
    custom_results = custom_parameters_example()
    
    print("\n🎉 All examples completed successfully!")
    print("=" * 70)
    print("📚 Key Takeaways:")
    print("1. Use dictionary format: {'Portfolio Name': ['Fund1', 'Fund2', ...]}")
    print("2. Call analyzer.analyze_portfolios() to get comparison table")
    print("3. Use built-in methods for export and visualization")
    print("4. All portfolios are equal-weighted automatically")
    print("5. XIRR is calculated for SIP investments")
    print("6. Comprehensive non-SIP metrics are included")
